# frozen_string_literal: true

require 'rails_helper'

RSpec.describe WhatsappTemplateService do
  let(:user)                          { create(:user) }
  let(:another_user)                  { create(:user, tenant_id: user.tenant_id) }
  let(:valid_auth_token)              { build(:auth_token, :with_whatsapp_template_read_all_update_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:auth_data)                     { User::TokenParser.parse(valid_auth_token.token) }
  let(:invalid_auth_token)            { build(:auth_token, :with_sms_delete_all_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:token_without_sms) { build(:auth_token, :without_sms_permission, user_id: user.id, tenant_id: user.tenant_id, username: user.name) }
  let(:auth_data_without_sms) { User::TokenParser.parse(token_without_sms.token) }
  let(:invalid_auth_data)             { User::TokenParser.parse(invalid_auth_token.token) }
  let(:template_media) { create(:template_media, tenant_id: user.tenant_id) }
  let(:end_time) { Date.today.in_time_zone('Asia/Calcutta').beginning_of_day.to_i }
  let(:whatsapp_credit){ create(:whatsapp_credit, tenant_id: user.tenant_id, total: 1000, consumed: 0, credits_revised_at: end_time) }
  let(:conversation) { create(:conversation, connected_account_id: connected_account.id, tenant_id: user.tenant_id, phone_number: '+************', owner_id: user.id) }
  let(:sub_conversation) { create(:sub_conversation, connected_account_id: connected_account.id, tenant_id: user.tenant_id, conversation_id: conversation.id) }

  describe '#get' do
    let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id, created_by: user) }
    let(:other_connected_account) { create(:connected_account, tenant_id: user.tenant_id, created_by: user) }
    let(:user_template) { create(:whatsapp_template, connected_account: connected_account, created_by: user) }
    let(:other_template) { create(:whatsapp_template, connected_account: other_connected_account, created_by: another_user) }

    before do
      Thread.current[:auth] = auth_data
      Thread.current[:user] = user
    end

    context 'when user has read only' do
      before do
        expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'read').and_return(true)
        expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'read_all').and_return(false)
      end

      context 'when user is template creator' do
        it 'returns template' do
          expect(described_class.new({ id: user_template.id }).get).to eq(user_template)
        end
      end

      context 'when user is connected account agent' do
        before { create(:agent_user, tenant_id: user.tenant_id, connected_account_id: other_connected_account.id, user_id: user.id) }

        it 'returns template' do
          expect(described_class.new({ id: other_template.id }).get).to eq(other_template)
        end
      end
    end

    context 'when user has read all' do
      it 'returns template' do
        expect(described_class.new({ id: other_template.id }).get).to eq(other_template)
      end
    end

    context 'when template not found' do
      context 'when template does not exist for tenant' do
        it 'raises error' do
          expect{ described_class.new({ id: -1 }).get }.to raise_error(ExceptionHandler::NotFound, '022006')
        end
      end

      context 'when user cannot access template' do
        before do
          expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'read').and_return(true)
          expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'read_all').and_return(false)
        end

        it 'raises error' do
          expect{ described_class.new({ id: other_template.id }).get }.to raise_error(ExceptionHandler::NotFound, '022006')
        end
      end
    end

    context 'when user does not have read' do
      before { Thread.current[:auth] = invalid_auth_data }

      it 'raises error' do
        expect(Rails.logger).to receive(:error).with("User doesn't have permission to read whatsapp template")
        expect{ described_class.new({ id: user_template.id }).get }.to raise_error(ExceptionHandler::AuthenticationError, '022002||Unauthorized access.')
      end
    end
  end

  describe '#search' do
    let(:params) { ActionController::Parameters.new({}).permit! }
    let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id, created_by: user) }
    let(:other_connected_account) { create(:connected_account, tenant_id: user.tenant_id, created_by: user) }

    before do
      Thread.current[:auth] = auth_data
      Thread.current[:user] = user
      create(:whatsapp_template, connected_account: create(:connected_account, created_by: create(:user, tenant_id: (user.tenant_id + 1))))
    end

    context 'when filters are not present' do
      let!(:user_template) { create(:whatsapp_template, connected_account: connected_account, created_by: user) }
      let!(:other_template) { create(:whatsapp_template, connected_account: other_connected_account, created_by: another_user) }

      context 'when user has read all on whatsapp templates' do
        it 'returns all whatsapp templates for tenant' do
          search_response = described_class.new(params).search

          expect(search_response.map(&:id)).to match_array([user_template.id, other_template.id])
        end
      end

      context 'when user does not have read all on whatsapp templates' do
        before do
          expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'read').and_return(true)
          expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'read_all').and_return(false)
          create(:agent_user, connected_account: other_connected_account, user: user, tenant_id: user.tenant_id)
        end

        it 'returns templates created by user and where user is added as agent' do
          search_response = described_class.new(params).search

          expect(search_response.map(&:id)).to match_array([user_template.id, other_template.id])
        end
      end
    end

    context 'when filters are present' do
      let!(:user_template) { create(:whatsapp_template, connected_account: connected_account, created_by: user) }
      let!(:other_template) { create(:whatsapp_template, connected_account: other_connected_account, created_by: another_user) }

      context 'when equal operator' do
        let(:params) do
          ActionController::Parameters.new({
            json_rule: {
              rules: [
                { field: 'id', type: 'long', operator: 'equal', value: user_template.id },
                { field: 'name', type: 'string', operator: 'equal', value: user_template.name },
                { field: 'category', type: 'long', operator: 'equal', value: user_template.category },
                { field: 'connectedAccount', type: 'long', operator: 'equal', value: user_template.connected_account_id },
                { field: 'status', type: 'long', operator: 'equal', value: user_template.status },
                { field: 'entityType', type: 'long', operator: 'equal', value: user_template.entity_type },
                { field: 'createdAt', type: 'date', operator: 'equal', value: user_template.created_at.iso8601(6) }
              ]
            }
          })
        end

        it 'returns template' do
          search_response = described_class.new(params).search

          expect(search_response.map(&:id)).to eq([user_template.id])
        end
      end

      context 'when not equal operator' do
        let(:params) do
          ActionController::Parameters.new({
            json_rule: {
              rules: [
                { field: 'id', type: 'long', operator: 'not_equal', value: user_template.id },
                { field: 'connectedAccount', type: 'long', operator: 'not_equal', value: user_template.connected_account_id },
                { field: 'createdAt', type: 'date', operator: 'not_equal', value: user_template.created_at.iso8601(6) }
              ]
            }
          })
        end

        it 'returns template' do
          search_response = described_class.new(params).search

          expect(search_response.map(&:id)).to eq([other_template.id])
        end
      end

      context 'when in operator' do
        let(:params) do
          ActionController::Parameters.new({
            json_rule: {
              rules: [
                { field: 'id', type: 'long', operator: 'in', value: [user_template.id, other_template.id] },
                { field: 'name', type: 'string', operator: 'in', value: [user_template.name, other_template.name] },
                { field: 'category', type: 'long', operator: 'in', value: [user_template.category] },
                { field: 'connectedAccount', type: 'long', operator: 'in', value: [user_template.connected_account_id, other_template.connected_account_id] },
                { field: 'status', type: 'long', operator: 'in', value: [user_template.status] },
                { field: 'entityType', type: 'long', operator: 'in', value: [user_template.entity_type] },
              ]
            }
          })
        end

        it 'returns template' do
          search_response = described_class.new(params).search

          expect(search_response.map(&:id)).to match_array([user_template.id, other_template.id])
        end
      end

      context 'when not in operator' do
        let(:params) do
          ActionController::Parameters.new({
            json_rule: {
              rules: [
                { field: 'id', type: 'long', operator: 'not_in', value: [user_template.id] },
                { field: 'connectedAccount', type: 'long', operator: 'not_in', value: [user_template.connected_account_id] },
              ]
            }
          })
        end

        it 'returns template' do
          search_response = described_class.new(params).search

          expect(search_response.map(&:id)).to eq([other_template.id])
        end
      end

      context 'when greater operator' do
        let(:params) do
          ActionController::Parameters.new({
            json_rule: {
              rules: [
                { field: 'id', type: 'double', operator: 'greater', value: user_template.id },
                { field: 'createdAt', type: 'date', operator: 'greater', value: user_template.created_at.iso8601(6) }
              ]
            }
          })
        end

        it 'returns template' do
          search_response = described_class.new(params).search

          expect(search_response.map(&:id)).to eq([other_template.id])
        end
      end

      context 'when less operator' do
        let(:params) do
          ActionController::Parameters.new({
            json_rule: {
              rules: [
                { field: 'id', type: 'double', operator: 'less', value: other_template.id },
                { field: 'createdAt', type: 'date', operator: 'less', value: other_template.created_at.iso8601(6) }
              ]
            }
          })
        end

        it 'returns template' do
          search_response = described_class.new(params).search

          expect(search_response.map(&:id)).to eq([user_template.id])
        end
      end

      context 'when greater or equal operator' do
        let(:params) do
          ActionController::Parameters.new({
            json_rule: {
              rules: [
                { field: 'id', type: 'double', operator: 'greater_or_equal', value: user_template.id },
                { field: 'createdAt', type: 'date', operator: 'greater_or_equal', value: user_template.created_at.iso8601(6) }
              ]
            }
          })
        end

        it 'returns template' do
          search_response = described_class.new(params).search

          expect(search_response.map(&:id)).to match_array([user_template.id, other_template.id])
        end
      end

      context 'when less or equal operator' do
        let(:params) do
          ActionController::Parameters.new({
            json_rule: {
              rules: [
                { field: 'id', type: 'double', operator: 'less_or_equal', value: other_template.id },
                { field: 'createdAt', type: 'date', operator: 'less_or_equal', value: other_template.created_at.iso8601(6) }
              ]
            }
          })
        end

        it 'returns template' do
          search_response = described_class.new(params).search

          expect(search_response.map(&:id)).to match_array([user_template.id, other_template.id])
        end
      end

      context 'when between operator' do
        let(:params) do
          ActionController::Parameters.new({
            json_rule: {
              rules: [
                { field: 'id', type: 'double', operator: 'between', value: [user_template.id, other_template.id] },
                { field: 'createdAt', type: 'date', operator: 'between', value: [user_template.created_at.iso8601(6), other_template.created_at.iso8601(6)] }
              ]
            }
          })
        end

        it 'returns template' do
          search_response = described_class.new(params).search

          expect(search_response.map(&:id)).to match_array([user_template.id, other_template.id])
        end
      end

      context 'when not between operator' do
        let(:params) do
          ActionController::Parameters.new({
            json_rule: {
              rules: [
                { field: 'id', type: 'double', operator: 'not_between', value: [user_template.id, other_template.id] },
                { field: 'createdAt', type: 'date', operator: 'not_between', value: [user_template.created_at.iso8601(6), other_template.created_at.iso8601(6)] }
              ]
            }
          })
        end

        it 'returns blank' do
          search_response = described_class.new(params).search

          expect(search_response.map(&:id)).to eq([])
        end
      end

      context 'when contains operator' do
        let(:params) do
          ActionController::Parameters.new({
            json_rule: {
              rules: [
                { field: 'name', type: 'string', operator: 'contains', value: 'sale' }
              ]
            }
          })
        end

        it 'returns template' do
          search_response = described_class.new(params).search

          expect(search_response.map(&:id)).to match_array([user_template.id, other_template.id])
        end
      end

      context 'when not contains operator' do
        let(:params) do
          ActionController::Parameters.new({
            json_rule: {
              rules: [
                { field: 'name', type: 'string', operator: 'not_contains', value: 'contact' }
              ]
            }
          })
        end

        it 'returns template' do
          search_response = described_class.new(params).search

          expect(search_response.map(&:id)).to match_array([user_template.id, other_template.id])
        end
      end

      context 'when begins with operator' do
        let(:params) do
          ActionController::Parameters.new({
            json_rule: {
              rules: [
                { field: 'name', type: 'string', operator: 'begins_with', value: 'lead' }
              ]
            }
          })
        end

        it 'returns template' do
          search_response = described_class.new(params).search

          expect(search_response.map(&:id)).to match_array([user_template.id, other_template.id])
        end
      end

      context 'when is null operator' do
        let(:params) do
          ActionController::Parameters.new({
            json_rule: {
              rules: [
                { field: 'id', type: 'long', operator: 'is_null', value: nil },
                { field: 'name', type: 'string', operator: 'is_empty', value: nil },
                { field: 'category', type: 'long', operator: 'is_null', value: nil },
                { field: 'connectedAccount', type: 'long', operator: 'is_null', value: nil },
                { field: 'status', type: 'long', operator: 'is_null', value: nil },
                { field: 'entityType', type: 'long', operator: 'is_null', value: nil },
                { field: 'createdAt', type: 'date', operator: 'is_null', value: nil }
              ]
            }
          })
        end

        it 'returns blank' do
          search_response = described_class.new(params).search

          expect(search_response.map(&:id)).to eq([])
        end
      end

      context 'when is not null operator' do
        let(:params) do
          ActionController::Parameters.new({
            json_rule: {
              rules: [
                { field: 'id', type: 'long', operator: 'is_not_null', value: nil },
                { field: 'name', type: 'string', operator: 'is_not_empty', value: nil },
                { field: 'category', type: 'long', operator: 'is_not_null', value: nil },
                { field: 'connectedAccount', type: 'long', operator: 'is_not_null', value: nil },
                { field: 'status', type: 'long', operator: 'is_not_null', value: nil },
                { field: 'entityType', type: 'long', operator: 'is_not_null', value: nil },
                { field: 'createdAt', type: 'date', operator: 'is_not_null', value: nil }
              ]
            }
          })
        end

        it 'returns template' do
          search_response = described_class.new(params).search

          expect(search_response.map(&:id)).to match_array([user_template.id, other_template.id])
        end
      end
    end

    context 'when invalid filterable field' do
      let(:params) { ActionController::Parameters.new({ json_rule: { rules: [{ field: 'tenantId', type: 'long', operator: 'equal', value: 1 }] } }).permit! }

      it 'raises error' do
        expect { described_class.new(params).search }.to raise_error(ExceptionHandler::InvalidDataError, '022003||Invalid filter params')
      end
    end

    context 'when invalid sortable field' do
      let(:params) { ActionController::Parameters.new({ sort: 'created_by,asc' }).permit! }

      it 'raises error' do
        expect { described_class.new(params).search }.to raise_error(ExceptionHandler::InvalidDataError, '022003||Invalid sorting params')
      end
    end

    context 'when user does not have read on whatsapp templates' do
      before { Thread.current[:auth] = invalid_auth_data }

      it 'raises error' do
        expect(Rails.logger).to receive(:error).with("User doesn't have permission to read whatsapp templates")
        expect { described_class.new(params).search }.to raise_error(ExceptionHandler::AuthenticationError, '022002||Unauthorized access.')
      end
    end
  end

  describe '#create' do
    let(:connected_account) { create(:connected_account, created_by: user) }
    let(:params) do
      permitted_params = UnderscorizeKeys.do((ActionController::Parameters.new(JSON.parse(file_fixture('whatsapp_templates/create-whatsapp-template-params.json').read)).permit!).to_h).with_indifferent_access
      permitted_params['connected_account']['id'] = connected_account.id
      permitted_params
    end
    let(:image_template_params) do
      params['components'] = params['components'].reject { |comp| comp['type'] == 'HEADER' } << ({ type: 'HEADER', format: 'IMAGE', value: template_media.id, media_type: 'STATIC' })
      params.with_indifferent_access
    end
    let(:invalid_image_template_params) do
      params['components'] = params['components'].reject { |comp| comp['type'] == 'HEADER' } << ({ type: 'HEADER', format: 'IMAGE', value: -1, media_type: 'STATIC' })
      params.with_indifferent_access
    end

    before do
      Thread.current[:auth] = auth_data
      Thread.current[:user] = user
    end

    context 'when valid parameters' do
      before { create(:agent_user, tenant_id: user.tenant_id, user_id: user.id, connected_account_id: connected_account.id) }

      it 'creates template with components, variable mappings' do
        expect { described_class.new(params).create }
          .to change(WhatsappTemplate, :count).by(1)
          .and change(WhatsappTemplateComponent.where(component_type: HEADER), :count).by(1)
          .and change(WhatsappTemplateComponent.where(component_type: BODY), :count).by(1)
          .and change(WhatsappTemplateComponent.where(component_type: FOOTER), :count).by(1)
          .and change(WhatsappTemplateComponent.where(component_type: BUTTON), :count).by(2)
          .and change(VariableMapping.where(component_type: HEADER), :count).by(1)
          .and change(VariableMapping.where(component_type: BODY), :count).by(3)
          .and change(VariableMapping.where(component_type: BUTTON_COPY_CODE), :count).by(1)
      end

      it 'creates template with correct attributes' do
        created_whatsapp_template = described_class.new(params).create

        expect(created_whatsapp_template.connected_account_id).to eq(connected_account.id)
        expect(created_whatsapp_template.entity_type).to eq('lead')
        expect(created_whatsapp_template.name).to eq('Seasonal Promotion Lead')
        expect(created_whatsapp_template.category).to eq('MARKETING')
        expect(created_whatsapp_template.language).to eq('en')
        expect(created_whatsapp_template.status).to eq('DRAFT')
        expect(created_whatsapp_template.tenant_id).to eq(user.tenant_id)
        expect(created_whatsapp_template.created_by).to eq(user)
        expect(created_whatsapp_template.updated_by).to eq(user)
      end

      it 'creates header component with correct attributes' do
        header_component = described_class.new(params).create.components.find_by(component_type: 'HEADER')

        expect(header_component.component_format).to eq('TEXT')
        expect(header_component.component_text).to eq('Our {{1}} is on!')
        expect(header_component.component_value).to be_nil
        expect(header_component.content).to eq({ "header_text" => ["a"] })
        expect(header_component.tenant_id).to eq(user.tenant_id)
      end

      it 'creates body component with correct attributes' do
        body_component = described_class.new(params).create.components.find_by(component_type: 'BODY')

        expect(body_component.component_format).to eq('TEXT')
        expect(body_component.component_text).to eq('Shop now through {{1}} and use code {{2}} to get {{3}} off of all merchandise.')
        expect(body_component.component_value).to be_nil
        expect(body_component.content).to eq({ "body_text" => [["a", "a", "a"]] })
        expect(body_component.tenant_id).to eq(user.tenant_id)
      end

      it 'creates footer component with correct attributes' do
        footer_component = described_class.new(params).create.components.find_by(component_type: 'FOOTER')

        expect(footer_component.component_format).to eq('TEXT')
        expect(footer_component.component_text).to eq('Use the buttons below to manage your marketing subscriptions')
        expect(footer_component.component_value).to be_nil
        expect(footer_component.content).to be_nil
        expect(footer_component.tenant_id).to eq(user.tenant_id)
      end

      it 'creates button components with correct attributes' do
        button_components = described_class.new(params).create.components.where(component_type: 'BUTTON')

        expect(button_components.map(&:component_format)).to match_array(['QUICK_REPLY', 'COPY_CODE'])
        expect(button_components.map(&:component_text)).to match_array(['Unsubscribe from Promos', '25OFF'])
        expect(button_components.map(&:component_value)).to match_array([nil, nil])
        expect(button_components.map(&:content)).to match_array([nil, nil])
        expect(button_components.map(&:position)).to match_array([1, 2])
        expect(button_components.map(&:tenant_id).uniq).to eq([user.tenant_id])
      end

      it 'creates blank header variable mapping' do
        header_mapping = described_class.new(params).create.variable_mappings.find_by(component_type: 'HEADER')

        expect(header_mapping.template_variable).to eq(1)
        expect(header_mapping.tenant_id).to eq(user.tenant_id)
        expect(header_mapping.parent_entity).to eq('lead')
        expect(header_mapping.entity).to be_nil
        expect(header_mapping.field_type).to be_nil
        expect(header_mapping.internal_name).to be_nil
        expect(header_mapping.fallback_value).to be_nil
      end

      it 'creates blank body variable mappings' do
        body_mappings = described_class.new(params).create.variable_mappings.where(component_type: 'BODY')

        expect(body_mappings.map(&:template_variable)).to match_array([1, 2, 3])
        expect(body_mappings.map(&:tenant_id).uniq).to eq([user.tenant_id])
        expect(body_mappings.map(&:parent_entity).uniq).to eq(['lead'])
        expect(body_mappings.map(&:entity)).to eq([nil, nil, nil])
        expect(body_mappings.map(&:field_type)).to eq([nil, nil, nil])
        expect(body_mappings.map(&:internal_name)).to eq([nil, nil, nil])
        expect(body_mappings.map(&:fallback_value)).to eq([nil, nil, nil])
      end

      it 'creates blank button copy code variable mapping' do
        button_copy_code_mapping = described_class.new(params).create.variable_mappings.find_by(component_type: 'BUTTON_COPY_CODE')

        expect(button_copy_code_mapping.template_variable).to eq(1)
        expect(button_copy_code_mapping.tenant_id).to eq(user.tenant_id)
        expect(button_copy_code_mapping.parent_entity).to eq('lead')
        expect(button_copy_code_mapping.entity).to be_nil
        expect(button_copy_code_mapping.field_type).to be_nil
        expect(button_copy_code_mapping.internal_name).to be_nil
        expect(button_copy_code_mapping.fallback_value).to be_nil
      end

      context 'when two url buttons with one variable each' do
        before do
          params['components'] << { type: 'BUTTON', format: 'URL', text: 'Shop', value: 'https://www.google.com?{{2}}', position: 3 }.with_indifferent_access
          params['components'] << { type: 'BUTTON', format: 'URL', text: 'Visit', value: 'https://www.google.com?{{1}}', position: 4 }.with_indifferent_access
        end

        it 'creates two variable buttons and two variables' do
          expect { described_class.new(params).create }
            .to change(WhatsappTemplateComponent.where(component_type: BUTTON, component_format: URL), :count).by(2)
            .and change(VariableMapping.where(component_type: BUTTON_URL), :count).by(2)
        end
      end

      context 'when header component format is image' do
        it 'creates template with attached template media' do
          header_component = described_class.new(image_template_params).create.components.find_by(component_type: 'HEADER')
          expect(header_component.component_format).to eq('IMAGE')
          expect(header_component.component_value).to eq(template_media.id.to_s)
          expect(header_component.template_media).to be_present
        end
      end
    end

    context 'when invalid parameters' do
      context 'when user does not have permission to create template' do
        before { expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'write').and_return(false) }

        it 'raises error' do
          expect(Rails.logger).to receive(:error).with("User doesn't have permission to create whatsapp templates")
          expect { described_class.new(params).create }.to raise_error(ExceptionHandler::AuthenticationError, "022002||Unauthorized access.")
        end
      end

      context 'when duplicate name' do
        before { create(:whatsapp_template, connected_account: connected_account, name: params['name']) }

        it 'raises error' do
          expect { described_class.new(params).create }.to raise_error(ExceptionHandler::InvalidDataError, "022020||Name has already been taken and Whatsapp template namespace has already been taken")
        end
      end

      context 'when invalid connected account id' do
        it 'raises error' do
          expect { described_class.new(params).create }.to raise_error(ExceptionHandler::InvalidDataError, "022020||Uhoh! You don't seem to have access to connected account. Please ensure you are added as an agent.")
        end
      end

      context 'when component tally mismatch' do
        before { create(:agent_user, tenant_id: user.tenant_id, user_id: user.id, connected_account_id: connected_account.id) }

        context 'when invalid media id is passed' do
          it 'raises invalid data error' do
            expect { described_class.new(invalid_image_template_params).create }.to raise_error(ExceptionHandler::InvalidDataError, '022020||Template media must be present when header type is media')
          end
        end

        context 'when more than one header component' do
          before { params['components'] << { type: 'HEADER' }.with_indifferent_access }

          it 'raises error' do
            expect { described_class.new(params).create }.to raise_error(ExceptionHandler::InvalidDataError, "022020||Please include 1 body, atmost 1 header, footer, and maximum of 10 buttons.")
          end
        end

        context 'when more than one footer component' do
          before { params['components'] << { type: 'FOOTER' }.with_indifferent_access }

          it 'raises error' do
            expect { described_class.new(params).create }.to raise_error(ExceptionHandler::InvalidDataError, "022020||Please include 1 body, atmost 1 header, footer, and maximum of 10 buttons.")
          end
        end

        context 'when body component is missing' do
          before { params['components'].reject!{ |component|  component[:type] == 'BODY' } }

          it 'raises error' do
            expect { described_class.new(params).create }.to raise_error(ExceptionHandler::InvalidDataError, "022020||Please include 1 body, atmost 1 header, footer, and maximum of 10 buttons.")
          end
        end

        context 'when more than one body component' do
          before { params['components'] << { type: 'BODY' }.with_indifferent_access }

          it 'raises error' do
            expect { described_class.new(params).create }.to raise_error(ExceptionHandler::InvalidDataError, "022020||Please include 1 body, atmost 1 header, footer, and maximum of 10 buttons.")
          end
        end

        context 'when more than ten button components' do
          before { 9.times { params['components'] << { type: 'BUTTON' }.with_indifferent_access } }

          it 'raises error' do
            expect { described_class.new(params).create }.to raise_error(ExceptionHandler::InvalidDataError, "022020||Please include 1 body, atmost 1 header, footer, and maximum of 10 buttons.")
          end
        end

        context 'when more than one phone number button' do
          before { 2.times { params['components'] << { type: 'BUTTON', format: 'PHONE_NUMBER' }.with_indifferent_access } }

          it 'raises error' do
            expect { described_class.new(params).create }.to raise_error(ExceptionHandler::InvalidDataError, "022020||Buttons are limited to 1 phone number, copy code and 2 url buttons.")
          end
        end

        context 'when more than one copy code button' do
          before { 2.times { params['components'] << { type: 'BUTTON', format: 'COPY_CODE' }.with_indifferent_access } }

          it 'raises error' do
            expect { described_class.new(params).create }.to raise_error(ExceptionHandler::InvalidDataError, "022020||Buttons are limited to 1 phone number, copy code and 2 url buttons.")
          end
        end

        context 'when more than two url buttons' do
          before { 3.times { params['components'] << { type: 'BUTTON', format: 'URL' }.with_indifferent_access } }

          it 'raises error' do
            expect { described_class.new(params).create }.to raise_error(ExceptionHandler::InvalidDataError, "022020||Buttons are limited to 1 phone number, copy code and 2 url buttons.")
          end
        end

        context 'when invalid quick reply button sequence' do
          context 'when quick reply buttons are in between' do
            before do
              params['components'] = params['components'].reject { |component| component[:type] == 'BUTTON' }
              params['components'] += [
                { type: 'BUTTON', format: 'URL', position: 1 },
                { type: 'BUTTON', format: 'QUICK_REPLY', position: 2 },
                { type: 'BUTTON', format: 'URL', position: 3 }
              ]
            end

            it 'raises error' do
              expect { described_class.new(params).create }.to raise_error(ExceptionHandler::InvalidDataError, "022020||All quick reply buttons should be categorized at the start or end.")
            end
          end

          context 'when buttons start with quick reply but are not in sequence' do
            before do
              params['components'] = params['components'].reject { |component| component[:type] == 'BUTTON' }
              params['components'] += [
                { type: 'BUTTON', format: 'QUICK_REPLY', position: 1 },
                { type: 'BUTTON', format: 'QUICK_REPLY', position: 2 },
                { type: 'BUTTON', format: 'URL', position: 3 },
                { type: 'BUTTON', format: 'QUICK_REPLY', position: 4 },
                { type: 'BUTTON', format: 'URL', position: 5 }
              ]
            end

            it 'raises error' do
              expect { described_class.new(params).create }.to raise_error(ExceptionHandler::InvalidDataError, "022020||All quick reply buttons should be categorized at the start or end.")
            end
          end

          context 'when buttons end with quick reply but are not in sequence' do
            before do
              params['components'] = params['components'].reject { |component| component[:type] == 'BUTTON' }
              params['components'] += [
                { type: 'BUTTON', format: 'PHONE_NUMBER', position: 1 },
                { type: 'BUTTON', format: 'QUICK_REPLY', position: 2 },
                { type: 'BUTTON', format: 'URL', position: 3 },
                { type: 'BUTTON', format: 'QUICK_REPLY', position: 4 },
                { type: 'BUTTON', format: 'QUICK_REPLY', position: 5 }
              ]
            end

            it 'raises error' do
              expect { described_class.new(params).create }.to raise_error(ExceptionHandler::InvalidDataError, "022020||All quick reply buttons should be categorized at the start or end.")
            end
          end

          context 'when invalid position' do
            before do
              params['components'] = params['components'].reject { |component| component[:type] == 'BUTTON' }
              params['components'] += [
                { type: 'BUTTON', format: 'QUICK_REPLY', position: 1 },
                { type: 'BUTTON', format: 'PHONE_NUMBER', position: 3 },
                { type: 'BUTTON', format: 'URL', position: 'a' },
                { type: 'BUTTON', format: 'COPY_CODE', position: 'b' }
              ]
            end

            it 'raises error' do
              expect { described_class.new(params).create }.to raise_error(ExceptionHandler::InvalidDataError, "022020||Please ensure all buttons are in sequence with unique number positions.")
            end
          end

          context 'when duplicate position' do
            before do
              params['components'] = params['components'].reject { |component| component[:type] == 'BUTTON' }
              params['components'] += [
                { type: 'BUTTON', format: 'QUICK_REPLY', position: 1 },
                { type: 'BUTTON', format: 'PHONE_NUMBER', position: 3 },
                { type: 'BUTTON', format: 'URL', position: '2' },
                { type: 'BUTTON', format: 'COPY_CODE', position: '2' }
              ]
            end

            it 'raises error' do
              expect { described_class.new(params).create }.to raise_error(ExceptionHandler::InvalidDataError, "022020||Please ensure all buttons are in sequence with unique number positions.")
            end
          end
        end

        context 'when invalid url button variables' do
          context 'when one url button is present' do
            context 'when url variable is not {{1}} or {{2}}' do
              before do
                params['components'] << { type: 'BUTTON', format: 'URL', text: 'Visit Website', value: 'https://kylas.io?ref={{3}}', position: 3 }.with_indifferent_access
              end

              it 'raises error' do
                expect { described_class.new(params).create }.to raise_error(ExceptionHandler::InvalidDataError, "022020||Please ensure button url variables are either 1 or 2 and unique in case of more than one url button.")
              end
            end
          end

          context 'when two url buttons are present' do
            context 'when {{1}} is duplicated' do
              before do
                params['components'] << { type: 'BUTTON', format: 'URL', text: 'Visit Website', value: 'https://kylas.io?ref={{1}}', position: 3 }.with_indifferent_access
                params['components'] << { type: 'BUTTON', format: 'URL', text: 'Visit Website', value: 'https://app.kylas.io?ref={{1}}', position: 4 }.with_indifferent_access
              end

              it 'raises error' do
                expect { described_class.new(params).create }.to raise_error(ExceptionHandler::InvalidDataError, "022020||Please ensure button url variables are either 1 or 2 and unique in case of more than one url button.")
              end
            end

            context 'when {{2}} is duplicated' do
              before do
                params['components'] << { type: 'BUTTON', format: 'URL', text: 'Visit Website', value: 'https://kylas.io?ref={{2}}', position: 3 }.with_indifferent_access
                params['components'] << { type: 'BUTTON', format: 'URL', text: 'Visit Website', value: 'https://app.kylas.io?ref={{2}}', position: 4 }.with_indifferent_access
              end

              it 'raises error' do
                expect { described_class.new(params).create }.to raise_error(ExceptionHandler::InvalidDataError, "022020||Please ensure button url variables are either 1 or 2 and unique in case of more than one url button.")
              end
            end

            context 'when one or both url variables is not {{1}} or {{2}}' do
              before do
                params['components'] << { type: 'BUTTON', format: 'URL', text: 'Visit Website', value: 'https://kylas.io?ref={{1}}', position: 3 }.with_indifferent_access
                params['components'] << { type: 'BUTTON', format: 'URL', text: 'Visit Website', value: 'https://app.kylas.io?ref={{3}}', position: 4 }.with_indifferent_access
              end

              it 'raises error' do
                expect { described_class.new(params).create }.to raise_error(ExceptionHandler::InvalidDataError, "022020||Please ensure button url variables are either 1 or 2 and unique in case of more than one url button.")
              end
            end
          end
        end
      end
    end
  end

  describe '#create_and_submit' do
    let(:connected_account) { create(:connected_account, created_by: user) }
    let(:params) do
      permitted_params = UnderscorizeKeys.do((ActionController::Parameters.new(JSON.parse(file_fixture('whatsapp_templates/create-whatsapp-template-params.json').read)).permit!).to_h).with_indifferent_access
      permitted_params['connected_account']['id'] = connected_account.id
      permitted_params
    end

    before do
      create(:agent_user, tenant_id: user.tenant_id, user_id: user.id, connected_account_id: connected_account.id)
      Thread.current[:auth] = auth_data
      Thread.current[:user] = user
    end

    context 'when valid paramaters' do
      before do
        expect(SubmitWhatsappTemplateJob).to receive(:perform_later).once
      end

      it 'creates whatsapp template' do
        expect { described_class.new(params).create_and_submit }.to change(WhatsappTemplate, :count).by(1)
      end

      it 'creates template with category and status as submitting' do
        whatsapp_template = described_class.new(params).create_and_submit

        expect(whatsapp_template.category).to eq('MARKETING')
        expect(whatsapp_template.status).to eq('SUBMITTING')
      end
    end

    context 'when error occurs while creating template' do
      before do
        expect(SubmitWhatsappTemplateJob).not_to receive(:perform_later)
        allow_any_instance_of(WhatsappTemplate).to receive(:save!).and_raise(ActiveRecord::RecordInvalid.new(WhatsappTemplate.new(status: 'invalid')))
      end

      it 'raises error and does not create whatsapp template' do
        expect { described_class.new(params).create_and_submit }
          .to raise_error(ExceptionHandler::InvalidDataError, '022020||')
          .and change(WhatsappTemplate, :count).by(0)
      end
    end
  end

  describe '#update' do
    let(:connected_account) { create(:connected_account, created_by: user) }
    let!(:whatsapp_template) { create(:whatsapp_template, connected_account: connected_account, updated_by: another_user, status: 'APPROVED') }
    let(:params) do
      permitted_params = UnderscorizeKeys.do((ActionController::Parameters.new(JSON.parse(file_fixture('whatsapp_templates/create-whatsapp-template-params.json').read)).permit!).to_h).with_indifferent_access
      permitted_params['id'] = whatsapp_template.id
      permitted_params['components'].find { |comp| comp['type'] == 'HEADER' }['id'] = whatsapp_template.components.find_by(component_type: 'HEADER').id
      permitted_params['components'].find { |comp| comp['type'] == 'BODY' }['id'] = whatsapp_template.components.find_by(component_type: 'BODY').id
      permitted_params['components'].find { |comp| comp['type'] == 'BODY' }['text'] += '{{4}}'
      permitted_params['components'].reject! { |comp| comp['type'] == 'FOOTER' }
      permitted_params['components'].find { |comp| comp['type'] == 'BUTTON' && comp['format'] == 'QUICK_REPLY' }['id'] = whatsapp_template.components.find_by(component_type: 'BUTTON', component_format: 'QUICK_REPLY').id
      permitted_params
    end

    def image_template_params(template_media_id = template_media.id)
      params['components'] = params['components'].reject { |comp| comp['type'] == 'HEADER' } << ({ type: 'HEADER', format: 'IMAGE', value: template_media_id, media_type: 'STATIC' })
      params.with_indifferent_access
    end

    before do
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: HEADER, template_variable: 1)
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 1)
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 2)
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 3)
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BUTTON_URL, template_variable: 1)
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BUTTON_COPY_CODE, template_variable: 1)
      Thread.current[:auth] = auth_data
      Thread.current[:user] = user
    end

    context 'when valid parameters' do
      before do
        expect(SubmitWhatsappTemplateJob).to receive(:perform_later).once
        allow(Publishers::WhatsappTemplateNameUpdatedPublisher).to receive(:call)
      end

      it 'updates template name' do
        expect(Publishers::WhatsappTemplateNameUpdatedPublisher).to receive(:call).once

        updated_whatsapp_template = described_class.new(params).update
        expect(updated_whatsapp_template.id).to eq(whatsapp_template.id)
        expect(updated_whatsapp_template.name).to eq(params['name'])
      end

      it 'updates status' do
        updated_whatsapp_template = described_class.new(params).update
        expect(updated_whatsapp_template.id).to eq(whatsapp_template.id)
        expect(updated_whatsapp_template.status).to eq('SUBMITTING')
      end

      it 'updates template updated by' do
        updated_whatsapp_template = described_class.new(params).update
        expect(updated_whatsapp_template.id).to eq(whatsapp_template.id)
        expect(updated_whatsapp_template.updated_by).to eq(user)
      end

      it 'updates template components, variable mappings' do
        expect { described_class.new(params).update }
          .to change(WhatsappTemplateComponent.where(component_type: HEADER), :count).by(0)
          .and change(WhatsappTemplateComponent.where(component_type: BODY), :count).by(0)
          .and change(WhatsappTemplateComponent.where(component_type: FOOTER), :count).by(-1)
          .and change(WhatsappTemplateComponent.where(component_type: BUTTON, component_format: 'PHONE_NUMBER'), :count).by(-1)
          .and change(WhatsappTemplateComponent.where(component_type: BUTTON, component_format: 'URL'), :count).by(-1)
          .and change(WhatsappTemplateComponent.where(id: whatsapp_template.components.find_by(component_format: COPY_CODE)), :count).by(-1)
          .and change(WhatsappTemplateComponent.where(id: whatsapp_template.components.find_by(component_format: QUICK_REPLY)), :count).by(0)
          .and change(VariableMapping.where(component_type: BODY, template_variable: 4), :count).by(1)
          .and change(VariableMapping.where(component_type: BUTTON_URL), :count).by(-1)
      end

      it 'adds variable mapping for new variable' do
        updated_whatsapp_template = described_class.new(params).update

        new_body_variable = updated_whatsapp_template.variable_mappings.find_by(component_type: BODY, template_variable: 4)
        expect(new_body_variable.entity).to be_nil
        expect(new_body_variable.field_type).to be_nil
        expect(new_body_variable.internal_name).to be_nil
        expect(new_body_variable.fallback_value).to be_nil
        expect(new_body_variable.parent_entity).to eq('lead')
        expect(new_body_variable.tenant_id).to eq(updated_whatsapp_template.tenant_id)
      end

      context 'when template name is same as existing' do
        before do
          expect(Publishers::WhatsappTemplateNameUpdatedPublisher).not_to receive(:call)
        end

        it 'should not publish whatsapp template name updated event' do
          whatsapp_template.update(name: params['name'])
          described_class.new(params).update
        end  
      end

      context 'when template has media header' do
        context 'when component id is not present in the request and component value (template_media_id) is unchanged' do
          it 'attaches the template_media to the newly created header component' do
            image_template_params
            updated_whatsapp_template = described_class.new(params).update
            new_header_component = updated_whatsapp_template.components.find_by(component_type: HEADER)
            expect(new_header_component.component_value).to eq("#{template_media.id}")
            expect(TemplateMedia.find(template_media.id).whatsapp_template_component_id).to eq(new_header_component.id)
          end
        end

        context 'when component id is not present in the request and component value (template_media_id) is changed' do
          let(:another_template_media) { create(:template_media, tenant_id: user.tenant_id) }

          before do
            existing_header_component = whatsapp_template.components.find_by(component_type: HEADER)
            existing_header_component.update(component_format: IMAGE, component_text: nil, component_value: template_media.id, content: nil, media_type: 'STATIC')
            template_media.update(whatsapp_template_component_id: existing_header_component.id)
          end

          it 'removes whatsapp_template_component_id from old template media' do
            image_template_params(another_template_media.id)
            updated_whatsapp_template = described_class.new(params).update

            new_header_component = updated_whatsapp_template.components.find_by(component_type: HEADER)
            expect(new_header_component.component_value).to eq("#{another_template_media.id}")
            expect(TemplateMedia.find(another_template_media.id).whatsapp_template_component_id).to eq(new_header_component.id)
            expect(TemplateMedia.find(template_media.id).whatsapp_template_component_id).to eq(nil)
          end
        end

        context 'when header component is removed' do
          before do
            existing_header_component = whatsapp_template.components.find_by(component_type: HEADER)
            existing_header_component.update(component_format: IMAGE, component_text: nil, component_value: template_media.id, content: nil, media_type: 'STATIC')
            template_media.update(whatsapp_template_component_id: existing_header_component.id)

            params['components'] = params['components'].reject { |comp| comp['type'] == 'HEADER' }
          end

          it 'removes whatsapp_template_component_id from template media' do
            described_class.new(params).update

            expect(TemplateMedia.find(template_media.id).whatsapp_template_component_id).to eq(nil)
          end
        end
      end
    end

    context 'when invalid parameters' do
      context 'when user does not have permission to update' do
        before { expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'update').and_return(false) }

        it 'raises error' do
          expect(Rails.logger).to receive(:error).with("User doesn't have permission to update whatsapp templates")
          expect { described_class.new(params).update }.to raise_error(ExceptionHandler::AuthenticationError, "022002||Unauthorized access.")
        end
      end

      context 'when user has read all but not update all' do
        before do
          whatsapp_template.update(created_by: create(:user, tenant_id: user.tenant_id))
          expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'update').and_return(true)
          expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'read').and_return(true)
          expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'read_all').and_return(true)
          expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'update_all').and_return(false)
        end

        it 'raises error' do
          expect(Rails.logger).to receive(:error).with("User doesn't have permission to update whatsapp templates")
          expect { described_class.new(params).update }.to raise_error(ExceptionHandler::AuthenticationError, "022002||Unauthorized access.")
        end
      end

      context 'when template status is flagged' do
        before { whatsapp_template.update(status: FLAGGED) }

        it 'raises error' do
          expect { described_class.new(params).update }.to raise_error(ExceptionHandler::InvalidDataError, "022020||Template with only Draft, Approved, Rejected, Paused or Inactive status can be updated.")
        end
      end

      context 'when duplicate name' do
        before { create(:whatsapp_template, connected_account: whatsapp_template.connected_account, name: params['name']) }

        it 'raises error' do
          expect { described_class.new(params).update }.to raise_error(ExceptionHandler::InvalidDataError, "022020||Name has already been taken")
        end
      end

      context 'when component tally mismatch' do
        context 'when more than one header component' do
          before { params['components'] << { type: 'HEADER' }.with_indifferent_access }

          it 'raises error' do
            expect { described_class.new(params).update }.to raise_error(ExceptionHandler::InvalidDataError, "022020||Please include 1 body, atmost 1 header, footer, and maximum of 10 buttons.")
          end
        end

        context 'when more than one footer component' do
          before { 2.times { params['components'] << { type: 'FOOTER' }.with_indifferent_access } }

          it 'raises error' do
            expect { described_class.new(params).update }.to raise_error(ExceptionHandler::InvalidDataError, "022020||Please include 1 body, atmost 1 header, footer, and maximum of 10 buttons.")
          end
        end

        context 'when body component is missing' do
          before { params['components'].reject!{ |component|  component[:type] == 'BODY' } }

          it 'raises error' do
            expect { described_class.new(params).update }.to raise_error(ExceptionHandler::InvalidDataError, "022020||Please include 1 body, atmost 1 header, footer, and maximum of 10 buttons.")
          end
        end

        context 'when more than one body component' do
          before { params['components'] << { type: 'BODY' }.with_indifferent_access }

          it 'raises error' do
            expect { described_class.new(params).update }.to raise_error(ExceptionHandler::InvalidDataError, "022020||Please include 1 body, atmost 1 header, footer, and maximum of 10 buttons.")
          end
        end

        context 'when more than ten button components' do
          before { 9.times { params['components'] << { type: 'BUTTON' }.with_indifferent_access } }

          it 'raises error' do
            expect { described_class.new(params).update }.to raise_error(ExceptionHandler::InvalidDataError, "022020||Please include 1 body, atmost 1 header, footer, and maximum of 10 buttons.")
          end
        end

        context 'when more than one phone number button' do
          before { 2.times { params['components'] << { type: 'BUTTON', format: 'PHONE_NUMBER' }.with_indifferent_access } }

          it 'raises error' do
            expect { described_class.new(params).update }.to raise_error(ExceptionHandler::InvalidDataError, "022020||Buttons are limited to 1 phone number, copy code and 2 url buttons.")
          end
        end

        context 'when more than one copy code button' do
          before { 2.times { params['components'] << { type: 'BUTTON', format: 'COPY_CODE' }.with_indifferent_access } }

          it 'raises error' do
            expect { described_class.new(params).update }.to raise_error(ExceptionHandler::InvalidDataError, "022020||Buttons are limited to 1 phone number, copy code and 2 url buttons.")
          end
        end

        context 'when more than two url buttons' do
          before { 3.times { params['components'] << { type: 'BUTTON', format: 'URL' }.with_indifferent_access } }

          it 'raises error' do
            expect { described_class.new(params).update }.to raise_error(ExceptionHandler::InvalidDataError, "022020||Buttons are limited to 1 phone number, copy code and 2 url buttons.")
          end
        end

        context 'when invalid quick reply button sequence' do
          context 'when quick reply buttons are in between' do
            before do
              params['components'] = params['components'].reject { |component| component[:type] == 'BUTTON' }
              params['components'] += [
                { type: 'BUTTON', format: 'URL', position: 1 },
                { type: 'BUTTON', format: 'QUICK_REPLY', position: 2 },
                { type: 'BUTTON', format: 'URL', position: 3 }
              ]
            end

            it 'raises error' do
              expect { described_class.new(params).update }.to raise_error(ExceptionHandler::InvalidDataError, "022020||All quick reply buttons should be categorized at the start or end.")
            end
          end

          context 'when buttons start with quick reply but are not in sequence' do
            before do
              params['components'] = params['components'].reject { |component| component[:type] == 'BUTTON' }
              params['components'] += [
                { type: 'BUTTON', format: 'QUICK_REPLY', position: 1 },
                { type: 'BUTTON', format: 'QUICK_REPLY', position: 2 },
                { type: 'BUTTON', format: 'URL', position: 3 },
                { type: 'BUTTON', format: 'QUICK_REPLY', position: 4 },
                { type: 'BUTTON', format: 'URL', position: 5 }
              ]
            end

            it 'raises error' do
              expect { described_class.new(params).update }.to raise_error(ExceptionHandler::InvalidDataError, "022020||All quick reply buttons should be categorized at the start or end.")
            end
          end

          context 'when buttons end with quick reply but are not in sequence' do
            before do
              params['components'] = params['components'].reject { |component| component[:type] == 'BUTTON' }
              params['components'] += [
                { type: 'BUTTON', format: 'PHONE_NUMBER', position: 1 },
                { type: 'BUTTON', format: 'QUICK_REPLY', position: 2 },
                { type: 'BUTTON', format: 'URL', position: 3 },
                { type: 'BUTTON', format: 'QUICK_REPLY', position: 4 },
                { type: 'BUTTON', format: 'QUICK_REPLY', position: 5 }
              ]
            end

            it 'raises error' do
              expect { described_class.new(params).update }.to raise_error(ExceptionHandler::InvalidDataError, "022020||All quick reply buttons should be categorized at the start or end.")
            end
          end

          context 'when invalid position' do
            before do
              params['components'] = params['components'].reject { |component| component[:type] == 'BUTTON' }
              params['components'] += [
                { type: 'BUTTON', format: 'QUICK_REPLY', position: 1 },
                { type: 'BUTTON', format: 'PHONE_NUMBER', position: 3 },
                { type: 'BUTTON', format: 'URL', position: 'a' },
                { type: 'BUTTON', format: 'COPY_CODE', position: 'b' }
              ]
            end

            it 'raises error' do
              expect { described_class.new(params).update }.to raise_error(ExceptionHandler::InvalidDataError, "022020||Please ensure all buttons are in sequence with unique number positions.")
            end
          end

          context 'when duplicate position' do
            before do
              params['components'] = params['components'].reject { |component| component[:type] == 'BUTTON' }
              params['components'] += [
                { type: 'BUTTON', format: 'QUICK_REPLY', position: 1 },
                { type: 'BUTTON', format: 'PHONE_NUMBER', position: 3 },
                { type: 'BUTTON', format: 'URL', position: '2' },
                { type: 'BUTTON', format: 'COPY_CODE', position: '2' }
              ]
            end

            it 'raises error' do
              expect { described_class.new(params).update }.to raise_error(ExceptionHandler::InvalidDataError, "022020||Please ensure all buttons are in sequence with unique number positions.")
            end
          end
        end
      end
    end
  end

  describe '#lookup' do
    before do
      Thread.current[:auth] = auth_data
      Thread.current[:user] = user
      @connected_account = create(:connected_account, created_by: user)
      create(:whatsapp_template, connected_account: @connected_account, name: "Lead Template approved", status: APPROVED)
      create(:whatsapp_template, connected_account: create(:connected_account, created_by: user), name: "Another Lead Template approved", status: APPROVED)
      create(:whatsapp_template, connected_account: @connected_account, name: "Lead Template unapproved")
      create(:whatsapp_template, connected_account: @connected_account, name: "Contact Template", entity_type: LOOKUP_CONTACT)
    end

    context 'with proper data' do
      it 'returns template data' do
        templates = described_class.new(ActionController::Parameters.new({ entity_type: 'lead', connected_account_id: @connected_account.id, q: "lead temp" })).lookup
        expect(templates.count).to eq(1)
        expect(templates[0].name).to eq('Lead Template approved')
      end

      it 'returns templates with only name as query' do
        templates = described_class.new(ActionController::Parameters.new({ q: "lead temp" })).lookup
        expect(templates.count).to eq(2)
      end
    end
  end

  describe '#deactivate' do
    let(:connected_account) { create(:connected_account, created_by: user) }
    let!(:whatsapp_template) { create(:whatsapp_template, connected_account: connected_account, updated_by: another_user, status: 'APPROVED') }
    let(:params) { { id: whatsapp_template.id } }

    before do
      Thread.current[:auth] = auth_data
      Thread.current[:user] = user
    end

    context 'when valid parameters' do
      it 'updates status' do
        updated_whatsapp_template = described_class.new(params).deactivate
        expect(updated_whatsapp_template.id).to eq(whatsapp_template.id)
        expect(updated_whatsapp_template.status).to eq('INACTIVE')
      end

      it 'updates template updated by' do
        updated_whatsapp_template = described_class.new(params).deactivate
        expect(updated_whatsapp_template.id).to eq(whatsapp_template.id)
        expect(updated_whatsapp_template.updated_by).to eq(user)
      end
    end

    context 'when invalid parameters' do
      context 'when user does not have permission to update' do
        before { expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'update').and_return(false) }

        it 'raises error' do
          expect(Rails.logger).to receive(:error).with("User doesn't have permission to update whatsapp templates")
          expect { described_class.new(params).deactivate }.to raise_error(ExceptionHandler::AuthenticationError, "022002||Unauthorized access.")
        end
      end

      context 'when user has read all but not update all' do
        before do
          whatsapp_template.update(created_by: create(:user, tenant_id: user.tenant_id))
          expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'update').and_return(true)
          expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'read').and_return(true)
          expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'read_all').and_return(true)
          expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'update_all').and_return(false)
        end

        it 'raises error' do
          expect(Rails.logger).to receive(:error).with("User doesn't have permission to update whatsapp templates")
          expect { described_class.new(params).deactivate }.to raise_error(ExceptionHandler::AuthenticationError, "022002||Unauthorized access.")
        end
      end

      context 'when template status is flagged' do
        before { whatsapp_template.update(status: FLAGGED) }

        it 'raises error' do
          expect { described_class.new(params).deactivate }.to raise_error(ExceptionHandler::InvalidDataError, "022020||Template with only Draft, Approved, Rejected, or Paused status can be deactivated.")
        end
      end
    end
  end

  describe '#preview' do
    let(:connected_account) { create(:connected_account, created_by: user) }
    let!(:whatsapp_template) { create(:whatsapp_template, connected_account: connected_account, updated_by: another_user, status: 'APPROVED') }

    before do
      whatsapp_template.components.where(component_type: BODY).update(component_text: "\
TEXT_FIELD - {{1}} || PHONE - {{2}} || SYSTEM_PICK_LIST - {{3}} || EMAIL - {{4}} || URL - {{5}} || \
PIPELINE - {{6}} || LOOKUP - {{7}} || NUMBER - {{8}} || DATE_PICKER - {{9}} || DATETIME_PICKER - {{10}} || \
MULTI_PICKLIST - {{11}} || fallback_value - {{12}} || Products - {{13}} || Lead_country - {{14}} || \
Tenant_country - {{15}} || Tenant_timezone - {{16}} || created_by_country - {{17}} || created_by_timezone - {{18}} || \
updated_by_country - {{19}} || updated_by_timezone - {{20}} || converted_by_timezone - {{21}} || converted_by_country_nil_fallback_value - {{22}}")
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: HEADER, template_variable: 1, parent_entity: 'lead', entity: 'lead', internal_name: 'firstName')
      @unmapped_variable = whatsapp_template.variable_mappings.new(component_type: BODY, template_variable: 1, parent_entity: nil, entity: nil, internal_name: nil, tenant_id: whatsapp_template.tenant_id)
      @unmapped_variable.save(validate: false)
      Thread.current[:auth] = auth_data
      Thread.current[:token] = valid_auth_token.token
      Thread.current[:user] = user
    end

    context 'when requested for preview' do
      context 'if template have variables' do
        context 'and they are not or partially mapped' do
          it 'raises error' do
            expect { described_class.new(ActionController::Parameters.new({ id: whatsapp_template.id, entity_type: 'lead', entity_id: 34343 })).preview }.to raise_error(ExceptionHandler::InvalidDataError, '022003||All template variables are not mapped')
          end
        end

        context 'and they are mapped' do
          before do
            @unmapped_variable.update!(parent_entity: 'lead', entity: 'lead', internal_name: 'firstName', field_type: 'TEXT_FIELD')
            create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 2, parent_entity: 'lead', entity: 'lead', internal_name: 'phoneNumbers', field_type: 'PHONE')
            create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 3, parent_entity: 'lead', entity: 'lead', internal_name: 'salutation', field_type: 'PICK_LIST')
            create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 4, parent_entity: 'lead', entity: 'lead', internal_name: 'emails', field_type: 'EMAIL')
            create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 5, parent_entity: 'lead', entity: 'lead', internal_name: 'linkedIn', field_type: 'URL')
            create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 6, parent_entity: 'lead', entity: 'lead', internal_name: 'pipeline', field_type: 'PIPELINE')
            create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 7, parent_entity: 'lead', entity: 'lead', internal_name: 'pipelineStage', field_type: 'LOOK_UP')
            create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 8, parent_entity: 'lead', entity: 'lead', internal_name: 'companyAnnualRevenue', field_type: 'NUMBER')
            create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 9, parent_entity: 'lead', entity: 'lead', internal_name: 'cfCustomDate', field_type: 'DATE_PICKER')
            create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 10, parent_entity: 'lead', entity: 'lead', internal_name: 'cfCustomDateTime', field_type: 'DATETIME_PICKER')
            create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 11, parent_entity: 'lead', entity: 'lead', internal_name: 'cfMultiPick', field_type: 'MULTI_PICKLIST')
            create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 12, parent_entity: 'lead', entity: 'lead', internal_name: 'score', field_type: 'NUMBER', fallback_value: 'FALLEN_BACK')
            create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 13, parent_entity: 'lead', entity: 'lead', internal_name: 'products', field_type: 'LOOK_UP', fallback_value: 'FALLEN_BACK')
            create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 14, parent_entity: 'lead', entity: 'lead', internal_name: 'country', field_type: 'PICK_LIST')
            create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 15, entity: 'tenant', parent_entity: 'lead', internal_name: 'country', field_type: 'PICK_LIST')
            create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 16, entity: 'tenant', parent_entity: 'lead', internal_name: 'timezone', field_type: 'PICK_LIST')
            create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 17, entity: 'createdBy', parent_entity: 'lead', internal_name: 'country', field_type: 'PICK_LIST')
            create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 18, entity: 'createdBy', parent_entity: 'lead', internal_name: 'timezone', field_type: 'PICK_LIST')
            create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 19, entity: 'updatedBy', parent_entity: 'lead', internal_name: 'country', field_type: 'PICK_LIST')
            create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 20, entity: 'updatedBy', parent_entity: 'lead', internal_name: 'timezone', field_type: 'PICK_LIST')
            create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 21, entity: 'convertedBy', parent_entity: 'lead', internal_name: 'timezone', field_type: 'PICK_LIST')
            create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 22, entity: 'convertedBy', parent_entity: 'lead', internal_name: 'country', field_type: 'PICK_LIST', fallback_value: nil)

            create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BUTTON_URL, template_variable: 1, parent_entity: 'lead')
            create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BUTTON_COPY_CODE, template_variable: 1, parent_entity: 'lead')
            allow(DateTime).to receive(:now).and_return('12:01 AM'.to_time)
            permissions = Thread.current[:auth].permissions.as_json.map do |permission|
              permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
              permission
            end

            token_without_pid = GenerateToken.new(Thread.current[:user].id, Thread.current[:auth].tenant_id, permissions, true).call

            stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
              body: {
                fields: %w[firstName phoneNumbers salutation emails linkedIn pipeline pipelineStage companyAnnualRevenue cfCustomDate cfCustomDateTime cfMultiPick score products country createdBy updatedBy convertedBy customFieldValues lastName ownerId],
                jsonRule: {
                  rules: [
                    {
                      operator: 'equal',
                      id: 'id',
                      field: 'id',
                      type: 'double',
                      value: 34343
                    }
                  ],
                  condition: 'AND',
                  valid: true
                }
              }.to_json,
              headers: {
                'Accept'=>'application/json',
                'Authorization'=>"Bearer #{token_without_pid}",
                'Content-Type'=>'application/json',
              }
            ).to_return(status: 200, body: file_fixture('get_lead_response.json').read, headers: {})

            stub_request(:get, "http://localhost:8081/v1/tenants").with(
              headers: {
              'Accept'=>'*/*',
              'Authorization'=>"Bearer #{valid_auth_token.token}",
              }
            ).to_return(status: 200, body: file_fixture('get_tenant_response.json').read, headers: {})

            stub_request(:get, "http://localhost:8086/v1/picklists/standard").with(
              headers: {
              'Accept'=>'*/*',
              'Authorization'=>"Bearer #{valid_auth_token.token}",
              }
            ).to_return(status: 200, body: file_fixture('standard_picklists.json').read, headers: {})

            user_resp = JSON.parse(file_fixture('get_users_response.json').read)
            user_resp['content'][2]['id'] = user.id
            stub_request(:post, "http://localhost:8081/v1/users/search").with(
              body: {
                fields: %w[timezone dateFormat country firstName lastName],
                jsonRule: {
                  rules: [
                    {
                      operator: 'in',
                      id: 'id',
                      field: 'id',
                      type: 'double',
                      value: "4010,4020,#{user.id}"
                    }
                  ],
                  condition: 'AND',
                  valid: true
                }
              }.to_json,
              headers: {
                'Accept'=>'application/json',
                'Authorization'=>"Bearer #{valid_auth_token.token}",
                'Content-Type'=>'application/json',
              }
            ).to_return(status: 200, body: user_resp.to_json, headers: {})
          end

          it 'returns variable replaced content' do
            response = described_class.new(ActionController::Parameters.new({ id: whatsapp_template.id, entity_type: 'lead', entity_id: 34343 })).preview
            expect(response.components.find { |r| r.component_type == BODY }.component_text).to eq("\
TEXT_FIELD - lead first name || PHONE - +91 **********, +91 **********, +65 ******** || SYSTEM_PICK_LIST - Mr || \
EMAIL - <EMAIL>, <EMAIL> || URL - https://linkedin.url || PIPELINE - Default Lead Pipeline || \
LOOKUP - Open || NUMBER - 2102 || DATE_PICKER - Jun 04,2024 || DATETIME_PICKER - Jun 03,2024 at12:00 pm IST || \
MULTI_PICKLIST - pick3, asdb, pick2 || fallback_value - FALLEN_BACK || Products - Repairing Tool || \
Lead_country - India || Tenant_country - India || Tenant_timezone - (GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi / Sri Jayawardenapura || \
created_by_country - India || created_by_timezone - (GMT+05:30) Chennai, Kolkata, Mumbai, New Delhi / Sri Jayawardenapura || \
updated_by_country - India || updated_by_timezone - (GMT-12:00) International Date Line West || converted_by_timezone - John || converted_by_country_nil_fallback_value - ")
            expect(response.components.find { |r| r.component_type == HEADER }.component_text).to eq('Our lead first name is on!')
          end
        end
      end
    end
  end

  describe '#send_message' do
    let(:connected_account) { create(:connected_account, created_by: user, status: ACTIVE) }
    let!(:whatsapp_template) { create(:whatsapp_template, connected_account: connected_account, updated_by: another_user, status: 'APPROVED') }
    let(:user_share_rule) { create(:share_rule, share_rule_id: 3654, name: 'Share rule name updated', tenant_id: user.tenant_id, from_id: 4010, to_id: user.id, from_type: 'USER', to_type: 'USER', entity_id: 34343, entity_type: 'LEAD') }

    context 'when user does not have sufficient whatsapp credits balance' do
      it 'raises error' do
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token.token
        Thread.current[:user] = user

        expect { described_class.new(ActionController::Parameters.new({ id: whatsapp_template.id, entity_type: 'lead', entity_id: 34343, phone_id: 207783 })).send_message }.to raise_error(ExceptionHandler::InsufficientWhatsappCreditsBalance, '022023||Insufficient whatsapp credits balance')
      end
    end

    context 'when connected account is not active' do
      before do
        whatsapp_credit
      end

      it 'raises error' do
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token.token
        Thread.current[:user] = user
        connected_account.update(status: INACTIVE)
        expect { described_class.new(ActionController::Parameters.new({ id: whatsapp_template.id, entity_type: 'lead', entity_id: 34343, phone_id: 207783 })).send_message }.to raise_error(ExceptionHandler::AccountNotActiveError, '022030||Connected account is not active')
      end
    end

    context 'for dynamic template media' do
      let(:dynamic_template_media) { create(:template_media, tenant_id: user.tenant_id) }
      let!(:whatsapp_dynmaic_media_template) { create(:whatsapp_template, connected_account: connected_account, updated_by: another_user, status: 'APPROVED') }

      before do
        allow(DateTime).to receive(:now).and_return(DateTime.new(2021, 1, 1, 12, 0, 0))
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token.token
        Thread.current[:user] = user
        whatsapp_credit

        permissions = Thread.current[:auth].permissions.as_json.map do |permission|
          permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
          permission
        end

        token_without_pid = GenerateToken.new(Thread.current[:user].id, Thread.current[:auth].tenant_id, permissions, true).call
        stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
          body: {
            fields: %w[phoneNumbers customFieldValues firstName lastName ownerId],
            jsonRule: {
              rules: [
                {
                  operator: 'equal',
                  id: 'id',
                  field: 'id',
                  type: 'double',
                  value: 34343
                }
              ],
              condition: 'AND',
              valid: true
            }
          }.to_json,
          headers: {
            'Accept'=>'application/json',
            'Authorization'=>"Bearer #{token_without_pid}",
            'Content-Type'=>'application/json',
          }
        ).to_return(status: 200, body: file_fixture('get_lead_response.json').read, headers: {})

        stub_request(:get, "http://localhost:8086/v1/picklists/standard").with(
          headers: {
          'Accept'=>'*/*',
          'Authorization'=>"Bearer #{valid_auth_token.token}",
          }
        ).to_return(status: 200, body: file_fixture('standard_picklists.json').read, headers: {})

        whatsapp_dynmaic_media_template.components.where(component_type: BODY).update_all(component_text: 'sample Text')
        whatsapp_dynmaic_media_template.components.where(component_type: HEADER).update_all(component_format: 'IMAGE', component_value: -1, media_type: 'DYNAMIC', component_text: 'sample Text')
        whatsapp_dynmaic_media_template.components.where(component_type: BUTTON).destroy_all
      end

      context 'when template media is not found' do
        let(:params) do
          ActionController::Parameters.new({
            id: whatsapp_dynmaic_media_template.id,
            entity_type: 'lead',
            entity_id: 34343,
            phone_id: 207783,
            dynamic_template_media_id: 124
          }).permit!
        end

        it 'raises invalid data error' do
          expect { described_class.new(params).send_message }.to raise_error(
            ExceptionHandler::InvalidDataError,
            "#{ErrorCode.invalid_data}||#{I18n.t('error.invalid_template.template_media_not_found')}"
          )
        end
      end

      context 'when dynamic template media is provided' do
        let(:dynamic_template_media) { create(:template_media, tenant_id: user.tenant_id, file_name: "tenant_#{user.tenant_id}/connected_account_#{connected_account.id}/sample_file_7a3a66be-11ce-4ec4-93aa-bb22b527eaf8.png") }
        let(:params) do
          ActionController::Parameters.new({
            id: whatsapp_dynmaic_media_template.id,
            entity_type: 'lead',
            entity_id: 34343,
            phone_id: 207783,
            dynamic_template_media_id: dynamic_template_media.id
          }).permit!
        end

        before do
          resource = instance_double(Aws::S3::Resource)
          bucket = instance_double(Aws::S3::Bucket)
          obj = instance_double(Aws::S3::Object)
          file_name = dynamic_template_media.file_name
          allow(Aws::S3::Resource).to receive(:new).and_return(resource)
          allow(resource).to receive(:bucket).with(S3_ATTACHMENT_BUCKET).and_return(bucket)
          allow(bucket).to receive(:object).with(file_name).and_return(obj)
          allow(obj).to receive(:presigned_url).with(:get, expires_in: 300, :response_content_disposition => "attachment; filename=#{dynamic_template_media.file_name.split('/', 3)[-1]}", :response_content_type => dynamic_template_media.file_type).and_return('https://www.aws.com/files/dynamic.mp3')
          conversation
          sub_conversation
          user_share_rule

          stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages").with(
          body: {
            messaging_product: 'whatsapp',
            recipient_type: 'individual',
            to: '+************',
            type: 'template',
            template: {
              name: whatsapp_dynmaic_media_template.whatsapp_template_namespace,
              language: {
                code: 'en'
              },
              components: [
                {
                  type: 'header',
                  parameters: [
                    {
                      type: 'image',
                      image: {
                        link: 'https://www.aws.com/files/dynamic.mp3'
                      }
                    }
                  ]
                },
                {
                  type: 'body',
                  parameters: []
                }
              ]
            }
          }.to_json,
          headers: {
            'x-access-token': 'partner-token',
            'x-waba-id': connected_account.waba_id,
            'Content-Type'=>'application/json',
          }).to_return(status: 200, body: file_fixture('/facebook/whatsapp_template/send-message-success-response.json'), headers: {})

          expect(CopyTemplateMediaToMessageJob).to receive(:perform_later).and_return(true)
        end

        it 'uses the dynamic template media instead of component media' do
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))
          expect { described_class.new(params).send_message }.to change(Message, :count).by(1)
          
          message = Message.last
          expect(message.attachments.first.file_name).to eq("tenant_#{user.tenant_id}/user_#{user.id}/attachments/#{message.id}_sample_file_#{DateTime.now.to_i}.png")
          expect(message.component_wise_content).to match_array(
            [
              { "id"=>a_kind_of(Integer), "text"=>"sample Text", "type"=>"HEADER", "value"=>"-1", "format"=>"IMAGE", "position"=>nil, "mediaType"=>"DYNAMIC" },
              { "id"=>a_kind_of(Integer), "text"=>"sample Text", "type"=>"BODY", "value"=>nil, "format"=>"TEXT", "position"=>nil },
              { "id"=>a_kind_of(Integer), "text"=>"Use the buttons below to manage your marketing subscriptions", "type"=>"FOOTER", "value"=>nil, "format"=>"TEXT", "position"=>nil }
            ]
          )
        end
      end

      context 'when dynamic template media is not found' do
        let(:params) do
          ActionController::Parameters.new({
            id: whatsapp_dynmaic_media_template.id,
            entity_type: 'lead',
            entity_id: 34343,
            phone_id: 207783,
            dynamic_template_media_id: -1
          }).permit!
        end

        it 'raises invalid data error' do
          expect { described_class.new(params).send_message }.to raise_error(
            ExceptionHandler::InvalidDataError,
            "#{ErrorCode.invalid_data}||#{I18n.t('error.invalid_template.template_media_not_found')}"
          )
        end
      end
    end

    context 'when requested for send message' do
      before do
        whatsapp_template.components.where(component_type: BODY).update(component_text: 'TEXT_FIELD - {{1}} || PHONE - {{2}} || SYSTEM_PICK_LIST - {{3}} || EMAIL - {{4}} || URL - {{5}} || PIPELINE - {{6}} || LOOKUP - {{7}} || NUMBER - {{8}} || DATE_PICKER - {{9}} || DATETIME_PICKER - {{10}} || MULTI_PICKLIST - {{11}} || fallback_value - {{12}}')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: HEADER, template_variable: 1, parent_entity: 'lead', entity: 'lead', internal_name: 'firstName')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 1, parent_entity: 'lead', entity: 'lead', internal_name: 'firstName')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 2, parent_entity: 'lead', entity: 'lead', internal_name: 'phoneNumbers', field_type: 'PHONE')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 3, parent_entity: 'lead', entity: 'lead', internal_name: 'salutation', field_type: 'PICK_LIST')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 4, parent_entity: 'lead', entity: 'lead', internal_name: 'emails', field_type: 'EMAIL')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 5, parent_entity: 'lead', entity: 'lead', internal_name: 'linkedIn', field_type: 'URL')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 6, parent_entity: 'lead', entity: 'lead', internal_name: 'pipeline', field_type: 'PIPELINE')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 7, parent_entity: 'lead', entity: 'lead', internal_name: 'pipelineStage', field_type: 'LOOK_UP')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 8, parent_entity: 'lead', entity: 'lead', internal_name: 'companyAnnualRevenue', field_type: 'NUMBER')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 9, parent_entity: 'lead', entity: 'lead', internal_name: 'cfCustomDate', field_type: 'DATE_PICKER')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 10, parent_entity: 'lead', entity: 'lead', internal_name: 'cfCustomDateTime', field_type: 'DATETIME_PICKER')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 11, parent_entity: 'lead', entity: 'lead', internal_name: 'cfMultiPick', field_type: 'MULTI_PICKLIST')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 12, parent_entity: 'lead', entity: 'lead', internal_name: 'score', field_type: 'NUMBER', fallback_value: 'FALLEN_BACK')

        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BUTTON_URL, template_variable: 1, entity: 'tenant', internal_name: 'accountName', parent_entity: 'lead')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BUTTON_COPY_CODE, template_variable: 1, parent_entity: 'lead')
        create(:url_component, whatsapp_template: whatsapp_template, component_value: 'https://www.staticUrl.com', position: 1)
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token.token
        Thread.current[:user] = user

        whatsapp_credit
        allow(DateTime).to receive(:now).and_return('12:01 AM'.to_time)
        permissions = Thread.current[:auth].permissions.as_json.map do |permission|
          permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
          permission
        end

        token_without_pid = GenerateToken.new(Thread.current[:user].id, Thread.current[:auth].tenant_id, permissions, true).call
        stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
          body: {
            fields: %w[firstName phoneNumbers salutation emails linkedIn pipeline pipelineStage companyAnnualRevenue cfCustomDate cfCustomDateTime cfMultiPick score createdBy customFieldValues lastName ownerId],
            jsonRule: {
              rules: [
                {
                  operator: 'equal',
                  id: 'id',
                  field: 'id',
                  type: 'double',
                  value: 34343
                }
              ],
              condition: 'AND',
              valid: true
            }
          }.to_json,
          headers: {
            'Accept'=>'application/json',
            'Authorization'=>"Bearer #{token_without_pid}",
            'Content-Type'=>'application/json',
          }
        ).to_return(status: 200, body: file_fixture('get_lead_response.json').read, headers: {})

        stub_request(:get, "http://localhost:8081/v1/tenants").with(
          headers: {
          'Accept'=>'*/*',
          'Authorization'=>"Bearer #{valid_auth_token.token}",
          }
        ).to_return(status: 200, body: file_fixture('get_tenant_response.json').read, headers: {})

        user_resp = JSON.parse(file_fixture('get_users_response.json').read)
        user_resp['content'][2]['id'] = user.id
        stub_request(:post, "http://localhost:8081/v1/users/search").with(
          body: {
            fields: %w[timezone dateFormat firstName lastName],
            jsonRule: {
              rules: [
                {
                  operator: 'in',
                  id: 'id',
                  field: 'id',
                  type: 'double',
                  value: "4010,#{user.id}"
                }
              ],
              condition: 'AND',
              valid: true
            }
          }.to_json,
          headers: {
            'Accept'=>'application/json',
            'Authorization'=>"Bearer #{valid_auth_token.token}",
            'Content-Type'=>'application/json',
          }
        ).to_return(status: 200, body: user_resp.to_json, headers: {})

        stub_request(:get, "http://localhost:8086/v1/picklists/standard").with(
          headers: {
          'Accept'=>'*/*',
          'Authorization'=>"Bearer #{valid_auth_token.token}",
          }
        ).to_return(status: 200, body: file_fixture('standard_picklists.json').read, headers: {})


        stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages").with(
          body: {
            messaging_product: 'whatsapp',
            recipient_type: 'individual',
            to: '+************',
            type: 'template',
            template: {
              name: whatsapp_template.whatsapp_template_namespace,
              language: {
                code: 'en'
              },
              components: [
                {
                  type: 'header',
                  parameters: [
                    {
                      type: 'text',
                      text: 'lead first name'
                    }
                  ]
                },
                {
                  type: 'body',
                  parameters: [
                    {
                      type: 'text',
                      text: 'lead first name'
                    },
                    {
                      type: 'text',
                      text: '+91 **********, +91 **********, +65 ********'
                    },
                    {
                      type: 'text',
                      text: 'Mr'
                    },
                    {
                      type: 'text',
                      text: '<EMAIL>, <EMAIL>'
                    },
                    {
                      type: 'text',
                      text: 'https://linkedin.url'
                    },
                    {
                      type: 'text',
                      text: 'Default Lead Pipeline'
                    },
                    {
                      type: 'text',
                      text: 'Open'
                    },
                    {
                      type: 'text',
                      text: '2102'
                    },
                    {
                      type: 'text',
                      text: 'Jun 04,2024'
                    },
                    {
                      type: 'text',
                      text: 'Jun 03,2024 at12:00 pm IST'
                    },
                    {
                      type: 'text',
                      text: 'pick3, asdb, pick2'
                    },
                    {
                      type: 'text',
                      text: 'FALLEN_BACK'
                    }
                  ]
                },
                {
                  type: 'button',
                  sub_type: 'copy_code',
                  index: 2,
                  parameters: [
                    {
                      type: 'coupon_code',
                      coupon_code: 'Shubham'
                    }
                  ]
                },
                {
                  type: 'button',
                  sub_type: 'url',
                  index: 3,
                  parameters: [
                    {
                      type: 'text',
                      text: 'Account%20name'
                    }
                  ]
                },
                {
                  type: 'button',
                  sub_type: 'quick_reply',
                  index: 4,
                  parameters: [
                    {
                      type: 'payload',
                      payload: 'Unsubcribe from Promos'
                    }
                  ]
                }
              ]
            }
          }.to_json,
          headers: {
            'x-access-token': 'partner-token',
            'x-waba-id': connected_account.waba_id,
            'Content-Type'=>'application/json',
          }).to_return(status: 200, body: file_fixture('/facebook/whatsapp_template/send-message-success-response.json'), headers: {})
      end

      context 'when user does not have required conversation permission' do
        before(:each) do
          conversation
          sub_conversation
        end

        it 'raises error' do
          expect { described_class.new(ActionController::Parameters.new({ id: whatsapp_template.id, entity_type: 'lead', entity_id: 34343, phone_id: 207783 })).send_message }.to raise_error(ExceptionHandler::MessageNotAllowedError, '022007')
        end
      end

      context 'when user has required conversation permission' do
        before(:each) do
          user_share_rule
        end

        context 'when conversation not found for given phone number and connected account' do
          context 'when user has permission to create conversation' do
            before do
              expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))
              allow(AssociateConversationWithEntitiesJob).to receive(:perform_later)
            end

            it 'creates conversation' do
              expect { described_class.new(ActionController::Parameters.new({ id: whatsapp_template.id, entity_type: 'lead', entity_id: 34343, phone_id: 207783 })).send_message }.to change(Conversation, :count).by(1)
            end
          end

          context 'when user does not have permission to create conversation' do
            before do
              user_share_rule.destroy
            end

            it 'raises error' do
              expect { described_class.new(ActionController::Parameters.new({ id: whatsapp_template.id, entity_type: 'lead', entity_id: 34343, phone_id: 207783 })).send_message }.to raise_error(ExceptionHandler::MessageNotAllowedError, '022007')
            end
          end
        end

        context 'when conversation found for given phone number and connected account' do
          before(:each) do
            conversation
            sub_conversation
          end

          it 'sends message' do
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))
            expect { described_class.new(ActionController::Parameters.new({ id: whatsapp_template.id, entity_type: 'lead', entity_id: 34343, phone_id: 207783 })).send_message }.to change(Message, :count).by(1)

            message = Message.last
            expect(message.content).to eq("Our lead first name is on!\nTEXT_FIELD - lead first name || PHONE - +91 **********, +91 **********, +65 ******** || SYSTEM_PICK_LIST - Mr || EMAIL - <EMAIL>, <EMAIL> || URL - https://linkedin.url || PIPELINE - Default Lead Pipeline || LOOKUP - Open || NUMBER - 2102 || DATE_PICKER - Jun 04,2024 || DATETIME_PICKER - Jun 03,2024 at12:00 pm IST || MULTI_PICKLIST - pick3, asdb, pick2 || fallback_value - FALLEN_BACK\nUse the buttons below to manage your marketing subscriptions\nCall Us\nShop Now\nShubham\nShop Now\nUnsubcribe from Promos")
            expect(message.direction).to eq('outgoing')
            expect(message.status).to eq('sending')
            expect(message.recipient_number).to eq('+************')
            expect(message.sender_number).to eq(connected_account.waba_number)
            expect(message.remote_id).to eq('wamid.ID')
            expect(message.conversation_id).to eq(conversation.id)
            expect(message.sub_conversation_id).to eq(sub_conversation.id)
            expect(message.component_wise_content).to match(
              [
                {"id"=>a_kind_of(Integer), "text"=>"Our lead first name is on!", "type"=>"HEADER", "value"=>nil, "format"=>"TEXT", "position"=>nil},
                {
                  "id"=>a_kind_of(Integer),
                  "text"=>
                  "TEXT_FIELD - lead first name || PHONE - +91 **********, +91 **********, +65 ******** || SYSTEM_PICK_LIST - Mr || EMAIL - <EMAIL>, <EMAIL> || URL - https://linkedin.url || PIPELINE - Default Lead Pipeline || LOOKUP - Open || NUMBER - 2102 || DATE_PICKER - Jun 04,2024 || DATETIME_PICKER - Jun 03,2024 at12:00 pm IST || MULTI_PICKLIST - pick3, asdb, pick2 || fallback_value - FALLEN_BACK",
                  "type"=>"BODY",
                  "value"=>nil,
                  "format"=>"TEXT",
                  "position"=>nil
                },
                {"id"=>a_kind_of(Integer), "text"=>"Use the buttons below to manage your marketing subscriptions", "type"=>"FOOTER", "value"=>nil, "format"=>"TEXT", "position"=>nil},
                {"id"=>a_kind_of(Integer), "text"=>"Call Us", "type"=>"BUTTON", "value"=>"+************", "format"=>"PHONE_NUMBER", "position"=>1},
                {"id"=>a_kind_of(Integer), "text"=>"Shubham", "type"=>"BUTTON", "value"=>nil, "format"=>"COPY_CODE", "position"=>2},
                {"id"=>a_kind_of(Integer), "text"=>"Shop Now", "type"=>"BUTTON", "value"=>"Account%20name", "format"=>"URL", "position"=>3},
                {"id"=>a_kind_of(Integer), "text"=>"Unsubcribe from Promos", "type"=>"BUTTON", "value"=>nil, "format"=>"QUICK_REPLY", "position"=>4},
                {"id"=>a_kind_of(Integer), "text"=>"Shop Now", "type"=>"BUTTON", "value"=>"https://www.staticUrl.com", "format"=>"URL", "position"=>1}
              ]
            )
            lookup = message.related_to.first
            expect(lookup.entity_type).to eq('lead')
            expect(lookup.entity_id).to eq(34343)
            expect(lookup.phone_number).to eq('************')
            expect(lookup.name).to eq('lead first name')
            expect(lookup.owner_id).to eq(4010)
          end

          it 'adds connected account id on message' do
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))

            described_class.new(ActionController::Parameters.new({ id: whatsapp_template.id, entity_type: 'lead', entity_id: 34343, phone_id: 207783 })).send_message

            expect(Message.last.connected_account_id).to eq(connected_account.id)
          end

          it 'adds whatsapp template id on message' do
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))

            described_class.new(ActionController::Parameters.new({ id: whatsapp_template.id, entity_type: 'lead', entity_id: 34343, phone_id: 207783 })).send_message

            expect(Message.last.whatsapp_template_id).to eq(whatsapp_template.id)
          end

          context 'when phone_id is invalid' do
            it 'raises error' do
              expect { described_class.new(ActionController::Parameters.new({ id: whatsapp_template.id, entity_type: 'lead', entity_id: 34343, phone_id: 20778 })).send_message }.to raise_error(ExceptionHandler::InvalidDataError, '022027||This number is no longer associated with this entity. To continue the conversation, please use an alternate number.')
            end
          end

          context 'when whatsapp template CTA has invalid url' do
            it 'raises error' do
              whatsapp_template.components.where(component_value: "https://www.kylas.io?referral={{1}}").update(component_value: "https://www.kylas.io/path/{{1}}")
              expect(Rails.logger).to receive(:error).with("WhatsappTemplateService Error while forming URL for Tenant id #{auth_data.tenant_id} Template Id #{whatsapp_template.id} message bad URI(is not URI?): \"https://www.kylas.io/path/Account name\"")

              expect { described_class.new(ActionController::Parameters.new({ id: whatsapp_template.id, entity_type: 'lead', entity_id: 34343, phone_id: 207783 })).send_message }.to raise_error(ExceptionHandler::InvalidDataError, "022022||Invalid url for whatsapp template CTA https://www.kylas.io/path/Account name")
            end
          end

          context 'when all sub conversation are marked as completed' do
            before(:each) do
              conversation.update(status: COMPLETED)
              sub_conversation.update(status: COMPLETED)
            end
            
            it 'should create a new sub conversation' do
              expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))

              expect { described_class.new(ActionController::Parameters.new({ id: whatsapp_template.id, entity_type: 'lead', entity_id: 34343, phone_id: 207783 })).send_message }.to change(SubConversation, :count).by(1)
            end

            it 'should mark conversation as new' do
              expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))

              expect(described_class.new(ActionController::Parameters.new({ id: whatsapp_template.id, entity_type: 'lead', entity_id: 34343, phone_id: 207783 })).send_message)

              expect(conversation.reload.status).to eq(NEW)
            end
          end

          context 'when sub conversation is in progress and there is incoming and outgoing messages ' do
             let!(:incoming_message1) { create(:message, conversation_id: conversation.id, sub_conversation_id: sub_conversation.id, direction: :incoming, connected_account_id: conversation.connected_account_id) }
             let!(:outgoing_message)  { create(:message, conversation_id: conversation.id, sub_conversation_id: sub_conversation.id, direction: :outgoing, connected_account_id: conversation.connected_account_id) }
             let!(:incoming_message2) { create(:message, conversation_id: conversation.id, sub_conversation_id: sub_conversation.id, direction: :incoming, connected_account_id: conversation.connected_account_id) }

            it 'should mark conversation as in progress' do
              expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))
              sub_conversation.update(status: IN_PROGRESS)

              described_class.new(ActionController::Parameters.new({ id: whatsapp_template.id, entity_type: 'lead', entity_id: 34343, phone_id: 207783 })).send_message

              expect(conversation.reload.status).to eq(IN_PROGRESS)
            end
          end

          context 'when sub conversation is in progress and there is only 2 outgoing messages ' do
             let!(:incoming_message1) { create(:message, conversation_id: conversation.id, sub_conversation_id: sub_conversation.id, direction: :outgoing) }
             let!(:incoming_message2) { create(:message, conversation_id: conversation.id, sub_conversation_id: sub_conversation.id, direction: :outgoing) }

            it 'should not mark conversation status' do
              expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))

              described_class.new(ActionController::Parameters.new({ id: whatsapp_template.id, entity_type: 'lead', entity_id: 34343, phone_id: 207783 })).send_message

              expect(conversation.reload.status).to eq(NEW)
            end
          end

          context 'when chatbot is in progress and user send manual message' do 
            before do
              conversation.update(chatbot_conversation_id: 1, chatbot_conversation_completed: false)
            end
           it 'should publish a event ChatbotConversationCompleted 'do
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))
            expect(PublishEvent).to receive(:call).with(instance_of(Event::ChatbotConversationCompleted))

            described_class.new(ActionController::Parameters.new({ id: whatsapp_template.id, entity_type: 'lead', entity_id: 34343, phone_id: 207783, is_manual: true })).send_message

            expect(conversation.reload.chatbot_conversation_completed).to eq(true)
           end
          end
        end

        context 'when conversation id is provided instead of phone_id' do
          context 'when conversation not found for given phone number and connected account' do
            it 'raises error' do
              expect { described_class.new(ActionController::Parameters.new({ id: whatsapp_template.id, entity_type: 'lead', entity_id: 34343, conversation_id: 207783 })).send_message }.to raise_error(ExceptionHandler::ConversationNotFoundError, '022026||Conversation not found')
            end
          end
  
          context 'when conversation found for given phone number and connected account' do
            before(:each) do
              conversation
              sub_conversation
            end
  
            it 'sends message' do
              expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))
              expect { described_class.new(ActionController::Parameters.new({ id: whatsapp_template.id, entity_type: 'lead', entity_id: 34343, conversation_id: conversation.id })).send_message }.to change(Message, :count).by(1)
  
              message = Message.last
              expect(message.content).to eq("Our lead first name is on!\nTEXT_FIELD - lead first name || PHONE - +91 **********, +91 **********, +65 ******** || SYSTEM_PICK_LIST - Mr || EMAIL - <EMAIL>, <EMAIL> || URL - https://linkedin.url || PIPELINE - Default Lead Pipeline || LOOKUP - Open || NUMBER - 2102 || DATE_PICKER - Jun 04,2024 || DATETIME_PICKER - Jun 03,2024 at12:00 pm IST || MULTI_PICKLIST - pick3, asdb, pick2 || fallback_value - FALLEN_BACK\nUse the buttons below to manage your marketing subscriptions\nCall Us\nShop Now\nShubham\nShop Now\nUnsubcribe from Promos")
              expect(message.direction).to eq('outgoing')
              expect(message.status).to eq('sending')
              expect(message.recipient_number).to eq('+************')
              expect(message.sender_number).to eq(connected_account.waba_number)
              expect(message.remote_id).to eq('wamid.ID')
              expect(message.conversation_id).to eq(conversation.id)
              expect(message.sub_conversation_id).to eq(sub_conversation.id)
              lookup = message.related_to.first
              expect(lookup.entity_type).to eq('lead')
              expect(lookup.entity_id).to eq(34343)
              expect(lookup.phone_number).to eq('************')
              expect(lookup.name).to eq('lead first name')
              expect(lookup.owner_id).to eq(4010)
            end
  
            context 'when conversation phone number is not present on entity' do
              it 'raises error' do
                conversation.update(phone_number: '+9189898')
                expect { described_class.new(ActionController::Parameters.new({ id: whatsapp_template.id, entity_type: 'lead', entity_id: 34343, conversation_id: conversation.id })).send_message }.to raise_error(ExceptionHandler::InvalidDataError, '022027||This number is no longer associated with this entity. To continue the conversation, please use an alternate number.')
              end
            end
          end

        context 'when conversation status is IN_PROGRESS' do
          before(:each) do
            conversation
            sub_conversation
            conversation.update(status: IN_PROGRESS)
          end

          it 'does not call ConversationStatusUpdater' do
            expect(ConversationStatusUpdater).not_to receive(:new)

            expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))

            described_class.new(ActionController::Parameters.new({ id: whatsapp_template.id, entity_type: 'lead', entity_id: 34343, phone_id: 207783 })).send_message
          end
        end

        context 'when conversation status is COMPLETED' do
          before(:each) do
            conversation
            sub_conversation
            conversation.update(status: COMPLETED)
          end

          it 'does not call ConversationStatusUpdater' do
            expect(ConversationStatusUpdater).not_to receive(:new)

            expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))

            described_class.new(ActionController::Parameters.new({ id: whatsapp_template.id, entity_type: 'lead', entity_id: 34343, phone_id: 207783 })).send_message
          end
        end
      end
      end

      context 'when template is media template' do
        let(:whatsapp_media_template) { create(:whatsapp_template, connected_account: connected_account, updated_by: another_user, status: 'APPROVED') }

        before(:each) do
          user_share_rule
          conversation
          sub_conversation
          whatsapp_media_template.components.where(component_type: BODY).update(component_text: 'sample text')
          header_component = whatsapp_media_template.components.where(component_type: HEADER).first
          template_media = create(:template_media, whatsapp_template_component_id: header_component.id, tenant_id: user.tenant_id)
          header_component.update(component_format: IMAGE, component_text: nil, component_value: template_media.id, content: nil, media_type: 'STATIC')
          whatsapp_media_template.components.where.not(component_type: [HEADER, BODY, FOOTER]).delete_all

          permissions = Thread.current[:auth].permissions.as_json.map do |permission|
            permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
            permission
          end
          token_without_pid = GenerateToken.new(Thread.current[:user].id, Thread.current[:auth].tenant_id, permissions, true).call
          stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
            body: {
              fields: %w[phoneNumbers customFieldValues firstName lastName ownerId],
              jsonRule: {
                rules: [
                  {
                    operator: 'equal',
                    id: 'id',
                    field: 'id',
                    type: 'double',
                    value: 34343
                  }
                ],
                condition: 'AND',
                valid: true
              }
            }.to_json,
            headers: {
              'Accept'=>'application/json',
              'Authorization'=>"Bearer #{token_without_pid}",
              'Content-Type'=>'application/json',
            }
          ).to_return(status: 200, body: file_fixture('get_lead_response.json').read, headers: {})

          stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages").with(
          body: {
            messaging_product: 'whatsapp',
            recipient_type: 'individual',
            to: '+************',
            type: 'template',
            template: {
              name: whatsapp_media_template.whatsapp_template_namespace,
              language: {
                code: 'en'
              },
              components: [
                {
                  type: 'header',
                  parameters: [{
                    type: 'image',
                    image: {
                      link: 'https://www.aws.com/files/7.mp3'
                    }
                  }]
                },
                {
                  type: 'body',
                  parameters: []
                }
              ]
            }
          }.to_json,
          headers: {
            'x-access-token': 'partner-token',
            'x-waba-id': connected_account.waba_id,
            'Content-Type'=>'application/json',
          }).to_return(status: 200, body: file_fixture('/facebook/whatsapp_template/send-message-success-response.json'), headers: {})

          resource = instance_double(Aws::S3::Resource)
          bucket = instance_double(Aws::S3::Bucket)
          obj = instance_double(Aws::S3::Object)
          file_name = template_media.file_name
          allow(Aws::S3::Resource).to receive(:new).and_return(resource)
          allow(resource).to receive(:bucket).with(S3_ATTACHMENT_BUCKET).and_return(bucket)
          allow(bucket).to receive(:object).with(file_name).and_return(obj)
          allow(obj).to receive(:presigned_url).with(:get, expires_in: 300, :response_content_disposition => "attachment; filename=#{template_media.file_name}", :response_content_type => template_media.file_type).and_return('https://www.aws.com/files/7.mp3')
          expect(CopyTemplateMediaToMessageJob).to receive(:perform_later).and_return(true)
        end

        it 'sends message' do
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))

          expect { described_class.new(ActionController::Parameters.new({ id: whatsapp_media_template.id, entity_type: 'lead', entity_id: 34343, phone_id: 207783 })).send_message }.to change(Message, :count).by(1)

          message = Message.last
          expect(message.content).to eq("\nsample text\nUse the buttons below to manage your marketing subscriptions")
          expect(message.direction).to eq('outgoing')
          expect(message.status).to eq('sending')
          expect(message.recipient_number).to eq('+************')
          expect(message.sender_number).to eq(connected_account.waba_number)
          expect(message.remote_id).to eq('wamid.ID')
          expect(message.conversation_id).to eq(conversation.id)
          expect(message.sub_conversation_id).to eq(sub_conversation.id)
          lookup = message.related_to.first
          expect(lookup.entity_type).to eq('lead')
          expect(lookup.entity_id).to eq(34343)
          expect(lookup.phone_number).to eq('************')
          expect(lookup.name).to eq('lead first name')
          expect(lookup.owner_id).to eq(4010)
        end
      end
    end

    context 'when user fields are not there in template and date fields are present in the template variables' do
      before do
        whatsapp_template.components.where(component_format: 'COPY_CODE').destroy_all
        whatsapp_template.components.where(component_type: BODY).update(component_text: 'TEXT_FIELD - {{1}} || PHONE - {{2}} || SYSTEM_PICK_LIST - {{3}} || EMAIL - {{4}} || URL - {{5}} || PIPELINE - {{6}} || LOOKUP - {{7}} || NUMBER - {{8}} || DATE_PICKER - {{9}} || DATETIME_PICKER - {{10}} || MULTI_PICKLIST - {{11}} || fallback_value - {{12}}')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: HEADER, template_variable: 1, parent_entity: 'lead', entity: 'lead', internal_name: 'firstName')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 1, parent_entity: 'lead', entity: 'lead', internal_name: 'firstName')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 2, parent_entity: 'lead', entity: 'lead', internal_name: 'phoneNumbers', field_type: 'PHONE')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 3, parent_entity: 'lead', entity: 'lead', internal_name: 'salutation', field_type: 'PICK_LIST')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 4, parent_entity: 'lead', entity: 'lead', internal_name: 'emails', field_type: 'EMAIL')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 5, parent_entity: 'lead', entity: 'lead', internal_name: 'linkedIn', field_type: 'URL')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 6, parent_entity: 'lead', entity: 'lead', internal_name: 'pipeline', field_type: 'PIPELINE')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 7, parent_entity: 'lead', entity: 'lead', internal_name: 'pipelineStage', field_type: 'LOOK_UP')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 8, parent_entity: 'lead', entity: 'lead', internal_name: 'companyAnnualRevenue', field_type: 'NUMBER')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 9, parent_entity: 'lead', entity: 'lead', internal_name: 'cfCustomDate', field_type: 'DATE_PICKER')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 10, parent_entity: 'lead', entity: 'lead', internal_name: 'cfCustomDateTime', field_type: 'DATETIME_PICKER')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 11, parent_entity: 'lead', entity: 'lead', internal_name: 'cfMultiPick', field_type: 'MULTI_PICKLIST')
        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 12, parent_entity: 'lead', entity: 'lead', internal_name: 'score', field_type: 'NUMBER', fallback_value: 'FALLEN_BACK')

        create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BUTTON_URL, template_variable: 1, entity: 'tenant', internal_name: 'accountName', parent_entity: 'lead')
        create(:url_component, whatsapp_template: whatsapp_template, component_value: 'https://www.staticUrl.com', position: 1)
        Thread.current[:auth] = auth_data
        Thread.current[:token] = valid_auth_token.token
        Thread.current[:user] = user

        whatsapp_credit
        user_share_rule
        conversation
        sub_conversation
        user.update(timezone: 'Asia/Calcutta', date_format: 'MMM D, YYYY [at] h:mm a')
        allow(DateTime).to receive(:now).and_return('12:01 AM'.to_time)
        permissions = Thread.current[:auth].permissions.as_json.map do |permission|
          permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
          permission
        end

        token_without_pid = GenerateToken.new(Thread.current[:user].id, Thread.current[:auth].tenant_id, permissions, true).call
        stub_request(:post, 'http://localhost:8083/v1/search/lead').with(
          body: {
            fields: %w[firstName phoneNumbers salutation emails linkedIn pipeline pipelineStage companyAnnualRevenue cfCustomDate cfCustomDateTime cfMultiPick score customFieldValues lastName ownerId],
            jsonRule: {
              rules: [
                {
                  operator: 'equal',
                  id: 'id',
                  field: 'id',
                  type: 'double',
                  value: 34343
                }
              ],
              condition: 'AND',
              valid: true
            }
          }.to_json,
          headers: {
            'Accept'=>'application/json',
            'Authorization'=>"Bearer #{token_without_pid}",
            'Content-Type'=>'application/json',
          }
        ).to_return(status: 200, body: file_fixture('get_lead_response.json').read, headers: {})

        stub_request(:get, "http://localhost:8081/v1/tenants").with(
          headers: {
          'Accept'=>'*/*',
          'Authorization'=>"Bearer #{valid_auth_token.token}",
          }
        ).to_return(status: 200, body: file_fixture('get_tenant_response.json').read, headers: {})

        stub_request(:get, "http://localhost:8086/v1/picklists/standard").with(
          headers: {
          'Accept'=>'*/*',
          'Authorization'=>"Bearer #{valid_auth_token.token}",
          }
        ).to_return(status: 200, body: file_fixture('standard_picklists.json').read, headers: {})

        stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages").with(
          body: {
            messaging_product: 'whatsapp',
            recipient_type: 'individual',
            to: '+************',
            type: 'template',
            template: {
              name: whatsapp_template.whatsapp_template_namespace,
              language: {
                code: 'en'
              },
              components: [
                {
                  type: 'header',
                  parameters: [
                    {
                      type: 'text',
                      text: 'lead first name'
                    }
                  ]
                },
                {
                  type: 'body',
                  parameters: [
                    {
                      type: 'text',
                      text: 'lead first name'
                    },
                    {
                      type: 'text',
                      text: '+91 **********, +91 **********, +65 ********'
                    },
                    {
                      type: 'text',
                      text: 'Mr'
                    },
                    {
                      type: 'text',
                      text: '<EMAIL>, <EMAIL>'
                    },
                    {
                      type: 'text',
                      text: 'https://linkedin.url'
                    },
                    {
                      type: 'text',
                      text: 'Default Lead Pipeline'
                    },
                    {
                      type: 'text',
                      text: 'Open'
                    },
                    {
                      type: 'text',
                      text: '2102'
                    },
                    {
                      type: 'text',
                      text: 'Jun 04,2024'
                    },
                    {
                      type: 'text',
                      text: 'Jun 03,2024 at12:00 pm IST'
                    },
                    {
                      type: 'text',
                      text: 'pick3, asdb, pick2'
                    },
                    {
                      type: 'text',
                      text: 'FALLEN_BACK'
                    }
                  ]
                },
                {
                  type: 'button',
                  sub_type: 'url',
                  index: 3,
                  parameters: [
                    {
                      type: 'text',
                      text: 'Account%20name'
                    }
                  ]
                },
                {
                  type: 'button',
                  sub_type: 'quick_reply',
                  index: 4,
                  parameters: [
                    {
                      type: 'payload',
                      payload: 'Unsubcribe from Promos'
                    }
                  ]
                }
              ]
            }
          }.to_json,
          headers: {
            'x-access-token': 'partner-token',
            'x-waba-id': connected_account.waba_id,
            'Content-Type'=>'application/json',
          }).to_return(status: 200, body: file_fixture('/facebook/whatsapp_template/send-message-success-response.json'), headers: {})
      end

      context 'when timezone and dateformat is present on the user' do
        it 'sends message by avoiding user search call' do
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))

          expect { described_class.new(ActionController::Parameters.new({ id: whatsapp_template.id, entity_type: 'lead', entity_id: 34343, phone_id: 207783 })).send_message }.to change(Message, :count).by(1)

          message = Message.last
          expect(message.content).to eq("Our lead first name is on!\nTEXT_FIELD - lead first name || PHONE - +91 **********, +91 **********, +65 ******** || SYSTEM_PICK_LIST - Mr || EMAIL - <EMAIL>, <EMAIL> || URL - https://linkedin.url || PIPELINE - Default Lead Pipeline || LOOKUP - Open || NUMBER - 2102 || DATE_PICKER - Jun 04,2024 || DATETIME_PICKER - Jun 03,2024 at12:00 pm IST || MULTI_PICKLIST - pick3, asdb, pick2 || fallback_value - FALLEN_BACK\nUse the buttons below to manage your marketing subscriptions\nCall Us\nShop Now\nShop Now\nUnsubcribe from Promos")
          expect(message.direction).to eq('outgoing')
          expect(message.status).to eq('sending')
          expect(message.recipient_number).to eq('+************')
          expect(message.sender_number).to eq(connected_account.waba_number)
          expect(message.remote_id).to eq('wamid.ID')
          expect(message.conversation_id).to eq(conversation.id)
          expect(message.sub_conversation_id).to eq(sub_conversation.id)
          lookup = message.related_to.first
          expect(lookup.entity_type).to eq('lead')
          expect(lookup.entity_id).to eq(34343)
          expect(lookup.phone_number).to eq('************')
          expect(lookup.name).to eq('lead first name')
          expect(lookup.owner_id).to eq(4010)
        end
      end
    end
  end

  describe '#send_bulk_message' do
    let(:connected_account) { create(:connected_account, created_by: user, status: ACTIVE) }
    let!(:whatsapp_template) { create(:whatsapp_template, connected_account: connected_account, updated_by: another_user, status: 'APPROVED') }
    let(:user_share_rule) { create(:share_rule, share_rule_id: 3654, name: 'Share rule name updated', tenant_id: user.tenant_id, from_id: 4010, to_id: user.id, from_type: 'USER', to_type: 'USER', entity_id: 34343, entity_type: 'LEAD') }

    def entity_data
      lead_response = JSON.parse(file_fixture('get_lead_response.json').read)
      entity_details = lead_response['content'][0]
      entity_details['metaData'] = lead_response['metaData']
      entity_details.with_indifferent_access
    end

    context 'when sufficient credits are not available for parking' do
      before do
        whatsapp_credit.update(parked: 800, consumed: 950)
      end

      context 'when low credits email is not sent' do
        before do
          expect(PublishEvent).to receive(:call).with(instance_of(Event::WhatsappCreditsAboutToExpire))
        end

        it 'raises error and send email' do
          expect(PublishEvent).not_to receive(:call).with(instance_of(Event::MessageCreated))
          expect{
            described_class.new(ActionController::Parameters.new({ id: whatsapp_template.id, entity_type: 'lead', entity_id: 34343, phone_id: 207783 })).send_bulk_message
          }.to raise_error(ExceptionHandler::InsufficientWhatsappCreditsBalance, '022028||Insufficient whatsapp credits balance for bulk or workflow action')

          expect(whatsapp_credit.reload.parked).to eq(800.0)
          expect(whatsapp_credit.reload.is_low_credits_email_sent).to eq(true)
        end
      end

      context 'when low credits email is already sent' do
        before do
          expect(PublishEvent).not_to receive(:call).with(instance_of(Event::WhatsappCreditsAboutToExpire))
        end

        it 'raises error but does not send email' do
          whatsapp_credit.update(is_low_credits_email_sent: true)
          expect{
            described_class.new(ActionController::Parameters.new({ id: whatsapp_template.id, entity_type: 'lead', entity_id: 34343, phone_id: 207783 })).send_bulk_message
          }.to raise_error(ExceptionHandler::InsufficientWhatsappCreditsBalance, '022028||Insufficient whatsapp credits balance for bulk or workflow action')

          expect(whatsapp_credit.reload.parked).to eq(800.0)
          expect(whatsapp_credit.reload.is_low_credits_email_sent).to eq(true)
        end
      end
    end

    context 'when whatsapp template is not approved or inactive' do
      before do
        whatsapp_credit
        whatsapp_template.update(status: REJECTED)
      end

      it 'raises error and does not send message' do
        expect(PublishEvent).not_to receive(:call).with(instance_of(Event::MessageCreated))
        expect{
          described_class.new({ id: whatsapp_template.id, entity_type: 'lead', entity_id: 34343, phone_number: entity_data[:phoneNumbers].first, entity_data: entity_data, bulk_job: true }).send_bulk_message
        }.to raise_error(ExceptionHandler::InactiveWhatsappTemplate, '022029||Whatsapp Template is not Approved')
      end
    end

    context 'when connected account is not active' do
      before do
        whatsapp_credit
        connected_account.update(status: INACTIVE)
      end

      it 'raises error and does not send message' do
        expect(PublishEvent).not_to receive(:call).with(instance_of(Event::MessageCreated))
        expect{
          described_class.new({ id: whatsapp_template.id, entity_type: 'lead', entity_id: 34343, phone_number: entity_data[:phoneNumbers].first, entity_data: entity_data, bulk_job: true }).send_bulk_message
        }.to raise_error(ExceptionHandler::AccountNotActiveError, '022030||Connected account is not active')
      end
    end

    

    before do
      whatsapp_template.components.where(component_type: BODY).update(component_text: 'TEXT_FIELD - {{1}} || PHONE - {{2}} || SYSTEM_PICK_LIST - {{3}} || EMAIL - {{4}} || URL - {{5}} || PIPELINE - {{6}} || LOOKUP - {{7}} || NUMBER - {{8}} || DATE_PICKER - {{9}} || DATETIME_PICKER - {{10}} || MULTI_PICKLIST - {{11}} || fallback_value - {{12}}')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: HEADER, template_variable: 1, parent_entity: 'lead', entity: 'lead', internal_name: 'firstName')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 1, parent_entity: 'lead', entity: 'lead', internal_name: 'firstName')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 2, parent_entity: 'lead', entity: 'lead', internal_name: 'phoneNumbers', field_type: 'PHONE')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 3, parent_entity: 'lead', entity: 'lead', internal_name: 'salutation', field_type: 'PICK_LIST')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 4, parent_entity: 'lead', entity: 'lead', internal_name: 'emails', field_type: 'EMAIL')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 5, parent_entity: 'lead', entity: 'lead', internal_name: 'linkedIn', field_type: 'URL')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 6, parent_entity: 'lead', entity: 'lead', internal_name: 'pipeline', field_type: 'PIPELINE')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 7, parent_entity: 'lead', entity: 'lead', internal_name: 'pipelineStage', field_type: 'LOOK_UP')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 8, parent_entity: 'lead', entity: 'lead', internal_name: 'companyAnnualRevenue', field_type: 'NUMBER')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 9, parent_entity: 'lead', entity: 'lead', internal_name: 'cfCustomDate', field_type: 'DATE_PICKER')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 10, parent_entity: 'lead', entity: 'lead', internal_name: 'cfCustomDateTime', field_type: 'DATETIME_PICKER')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 11, parent_entity: 'lead', entity: 'lead', internal_name: 'cfMultiPick', field_type: 'MULTI_PICKLIST')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BODY, template_variable: 12, parent_entity: 'lead', entity: 'lead', internal_name: 'score', field_type: 'NUMBER', fallback_value: 'FALLEN_BACK')

      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BUTTON_URL, template_variable: 1, entity: 'tenant', internal_name: 'accountName', parent_entity: 'lead')
      create(:variable_mapping, whatsapp_template: whatsapp_template, component_type: BUTTON_COPY_CODE, template_variable: 1, parent_entity: 'lead')
      create(:url_component, whatsapp_template: whatsapp_template, component_value: 'https://www.staticUrl.com', position: 1)
      Thread.current[:auth] = auth_data
      Thread.current[:token] = valid_auth_token.token
      Thread.current[:user] = user
    end

    context 'when requested for send bulk message' do
      before do
        whatsapp_credit
        allow(DateTime).to receive(:now).and_return('12:01 AM'.to_time)
        permissions = Thread.current[:auth].permissions.as_json.map do |permission|
          permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
          permission
        end

        token_without_pid = GenerateToken.new(Thread.current[:user].id, Thread.current[:auth].tenant_id, permissions, true).call

        stub_request(:get, "http://localhost:8081/v1/tenants").with(
          headers: {
          'Accept'=>'*/*',
          'Authorization'=>"Bearer #{valid_auth_token.token}",
          }
        ).to_return(status: 200, body: file_fixture('get_tenant_response.json').read, headers: {})

        user_resp = JSON.parse(file_fixture('get_users_response.json').read)
        user_resp['content'][2]['id'] = user.id
        stub_request(:post, "http://localhost:8081/v1/users/search").with(
          body: {
            fields: %w[timezone dateFormat firstName lastName],
            jsonRule: {
              rules: [
                {
                  operator: 'in',
                  id: 'id',
                  field: 'id',
                  type: 'double',
                  value: "4010,#{user.id}"
                }
              ],
              condition: 'AND',
              valid: true
            }
          }.to_json,
          headers: {
            'Accept'=>'application/json',
            'Authorization'=>"Bearer #{valid_auth_token.token}",
            'Content-Type'=>'application/json',
          }
        ).to_return(status: 200, body: user_resp.to_json, headers: {})

        stub_request(:get, "http://localhost:8086/v1/picklists/standard").with(
              headers: {
              'Accept'=>'*/*',
              'Authorization'=>"Bearer #{valid_auth_token.token}",
              }
            ).to_return(status: 200, body: file_fixture('standard_picklists.json').read, headers: {})

        stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages").with(
          body: {
            messaging_product: 'whatsapp',
            recipient_type: 'individual',
            to: '+************',
            type: 'template',
            template: {
              name: whatsapp_template.whatsapp_template_namespace,
              language: {
                code: 'en'
              },
              components: [
                {
                  type: 'header',
                  parameters: [
                    {
                      type: 'text',
                      text: 'lead first name'
                    }
                  ]
                },
                {
                  type: 'body',
                  parameters: [
                    {
                      type: 'text',
                      text: 'lead first name'
                    },
                    {
                      type: 'text',
                      text: '+91 **********, +91 **********, +65 ********'
                    },
                    {
                      type: 'text',
                      text: 'Mr'
                    },
                    {
                      type: 'text',
                      text: '<EMAIL>, <EMAIL>'
                    },
                    {
                      type: 'text',
                      text: 'https://linkedin.url'
                    },
                    {
                      type: 'text',
                      text: 'Default Lead Pipeline'
                    },
                    {
                      type: 'text',
                      text: 'Open'
                    },
                    {
                      type: 'text',
                      text: '2102'
                    },
                    {
                      type: 'text',
                      text: 'Jun 04,2024'
                    },
                    {
                      type: 'text',
                      text: 'Jun 03,2024 at12:00 pm IST'
                    },
                    {
                      type: 'text',
                      text: 'pick3, asdb, pick2'
                    },
                    {
                      type: 'text',
                      text: 'FALLEN_BACK'
                    }
                  ]
                },
                {
                  type: 'button',
                  sub_type: 'copy_code',
                  index: 2,
                  parameters: [
                    {
                      type: 'coupon_code',
                      coupon_code: 'Shubham'
                    }
                  ]
                },
                {
                  type: 'button',
                  sub_type: 'url',
                  index: 3,
                  parameters: [
                    {
                      type: 'text',
                      text: 'Account%20name'
                    }
                  ]
                },
                {
                  type: 'button',
                  sub_type: 'quick_reply',
                  index: 4,
                  parameters: [
                    {
                      type: 'payload',
                      payload: 'Unsubcribe from Promos'
                    }
                  ]
                }
              ]
            }
          }.to_json,
          headers: {
            'x-access-token': 'partner-token',
            'x-waba-id': connected_account.waba_id,
            'Content-Type'=>'application/json',
          }).to_return(status: 200, body: file_fixture('/facebook/whatsapp_template/send-message-success-response.json'), headers: {})
      end

      context 'when user does not have conversation permission while sending bulk message' do
        it 'raise error and does not send message' do
          expect(PublishEvent).not_to receive(:call).with(instance_of(Event::MessageCreated))
          expect{
            described_class.new({ id: whatsapp_template.id, entity_type: 'lead', entity_id: 34343, phone_number: entity_data[:phoneNumbers].first, entity_data: entity_data, bulk_job: true }).send_bulk_message
          }.to raise_error(ExceptionHandler::MessageNotAllowedError, "022007||You don't have required conversation permissions to send message")
        end
      end

      context 'when user have conversation permission while sending bulk message' do
        context 'when conversation not found for given phone number and connected account' do
          before do
            expect(AssociateConversationWithEntitiesJob).to receive(:perform_later).with(an_instance_of(Integer), 'lead', { dialCode: '+91', value: '**********' })
            expect(AssociateConversationWithEntitiesJob).to receive(:perform_later).with(an_instance_of(Integer), 'contact', { dialCode: '+91', value: '**********' })
          end

          it 'creates new conversation and sends message' do
            updated_entity_data = entity_data
            updated_entity_data['ownerId'] = user.id
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))

            expect {
              described_class.new({ id: whatsapp_template.id, entity_type: 'lead', entity_id: 34343, phone_number: entity_data[:phoneNumbers].first, entity_data: updated_entity_data, bulk_job: true }).send_bulk_message
            }.to change(Message, :count).by(1)
            .and change(Conversation, :count).by(1)
            .and change(SubConversation, :count).by(1)
          end
        end
      end

      context 'when conversation not found for given phone number and connected account' do
        before do
          expect(AssociateConversationWithEntitiesJob).to receive(:perform_later).with(an_instance_of(Integer), 'lead', { dialCode: '+91', value: '**********' })
          expect(AssociateConversationWithEntitiesJob).to receive(:perform_later).with(an_instance_of(Integer), 'contact', { dialCode: '+91', value: '**********' })
        end

        it 'creates new conversation and sends message' do
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))

          expect {
            described_class.new({ id: whatsapp_template.id, entity_type: 'lead', entity_id: 34343, phone_number: entity_data[:phoneNumbers].first, entity_data: entity_data }).send_bulk_message
          }.to change(Message, :count).by(1)
          .and change(Conversation, :count).by(1)
          .and change(SubConversation, :count).by(1)
        end
      end

      context 'when conversation found for given phone number and connected account' do
        before(:each) do
          conversation
          sub_conversation
        end

        context 'when sending message with workflow metadata' do
          let(:workflow_metadata) do
            {
              metadata: {
                executedWorkflows: ['WF_302']
              }
            }
          end

          it 'stores workflow metadata in message' do
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))

            described_class.new({ 
              id: whatsapp_template.id, 
              entity_type: 'lead', 
              entity_id: 34343, 
              phone_number: entity_data[:phoneNumbers].first, 
              entity_data: entity_data 
            }.merge(workflow_metadata)).send_bulk_message

            message = Message.last
            expect(message.metadata).to eq({ 'executedWorkflows' => ['WF_302'], 'retryConfig' => nil })
          end

          it 'stores empty array when no workflows provided' do
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))

            described_class.new({ 
              id: whatsapp_template.id, 
              entity_type: 'lead', 
              entity_id: 34343, 
              phone_number: entity_data[:phoneNumbers].first, 
              entity_data: entity_data 
            }).send_bulk_message

            message = Message.last
            expect(message.metadata).to eq({ 'executedWorkflows' => [], 'retryConfig' => nil })
          end
        end

        context 'when sending message with campaign info' do
          it 'stores campaign info in message' do
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))

            described_class.new({ 
              id: whatsapp_template.id, 
              entity_type: 'lead', 
              entity_id: 34343, 
              phone_number: entity_data[:phoneNumbers].first, 
              entity_data: entity_data,
              campaign_id: 123,
              activity_id: 456,
            }).send_bulk_message

            message = Message.last
            expect(message.campaign_info).to eq({"entity"=>"lead", "entityId"=>34343, "activityId"=>456, "campaignId"=>123, "phoneNumber"=>{"id"=>207783, "code"=>"IN", "type"=>"MOBILE", "value"=>"**********", "primary"=>true, "dialCode"=>"+91"}})
          end

          it 'stores empty hash when no campaign Info provided' do
            expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))

            described_class.new({ 
              id: whatsapp_template.id, 
              entity_type: 'lead', 
              entity_id: 34343, 
              phone_number: entity_data[:phoneNumbers].first, 
              entity_data: entity_data 
            }).send_bulk_message

            message = Message.last
            expect(message.campaign_info).to eq({})
          end
        end

        it 'sends message' do
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))

          expect { described_class.new({ id: whatsapp_template.id, entity_type: 'lead', entity_id: 34343, phone_number: entity_data[:phoneNumbers].first, entity_data: entity_data }).send_bulk_message }.to change(Message, :count).by(1)

          message = Message.last
          expect(message.content).to eq("Our lead first name is on!\nTEXT_FIELD - lead first name || PHONE - +91 **********, +91 **********, +65 ******** || SYSTEM_PICK_LIST - Mr || EMAIL - <EMAIL>, <EMAIL> || URL - https://linkedin.url || PIPELINE - Default Lead Pipeline || LOOKUP - Open || NUMBER - 2102 || DATE_PICKER - Jun 04,2024 || DATETIME_PICKER - Jun 03,2024 at12:00 pm IST || MULTI_PICKLIST - pick3, asdb, pick2 || fallback_value - FALLEN_BACK\nUse the buttons below to manage your marketing subscriptions\nCall Us\nShop Now\nShubham\nShop Now\nUnsubcribe from Promos")
          expect(message.direction).to eq('outgoing')
          expect(message.status).to eq('sending')
          expect(message.recipient_number).to eq('+************')
          expect(message.sender_number).to eq(connected_account.waba_number)
          expect(message.remote_id).to eq('wamid.ID')
          expect(message.conversation_id).to eq(conversation.id)
          expect(message.sub_conversation_id).to eq(sub_conversation.id)
          lookup = message.related_to.first
          expect(lookup.entity_type).to eq('lead')
          expect(lookup.entity_id).to eq(34343)
          expect(lookup.phone_number).to eq('************')
          expect(lookup.name).to eq('lead first name')
          expect(lookup.owner_id).to eq(4010)

          expect(whatsapp_credit.reload.parked).to eq(0.86)
        end

        it 'adds connected account id on message' do
          expect(PublishEvent).to receive(:call).with(instance_of(Event::MessageCreated))

          described_class.new({ id: whatsapp_template.id, entity_type: 'lead', entity_id: 34343, phone_number: entity_data[:phoneNumbers].first, entity_data: entity_data }).send_bulk_message

          expect(Message.last.connected_account_id).to eq(connected_account.id)
        end

        context 'when whatsapp template CTA has invalid url' do
          it 'raises error' do
            whatsapp_template.components.where(component_value: "https://www.kylas.io?referral={{1}}").update(component_value: "https://www.kylas.io/path/{{1}}")
            expect(Rails.logger).to receive(:error).with("WhatsappTemplateService Error while forming URL for Tenant id #{auth_data.tenant_id} Template Id #{whatsapp_template.id} message bad URI(is not URI?): \"https://www.kylas.io/path/Account name\"")

            expect { described_class.new({ id: whatsapp_template.id, entity_type: 'lead', entity_id: 34343, phone_number: entity_data[:phoneNumbers].first, entity_data: entity_data }).send_bulk_message }.to raise_error(ExceptionHandler::InvalidDataError, "022022||Invalid url for whatsapp template CTA https://www.kylas.io/path/Account name")
          end
        end
      end
    end
  end

  describe '#sync_status' do
    let(:connected_account) { create(:connected_account, created_by: user) }
    let!(:whatsapp_template) { create(:whatsapp_template, connected_account: connected_account, updated_by: another_user) }
    let(:params) { {id: whatsapp_template.id} }

    before do
      Thread.current[:auth] = auth_data
      Thread.current[:user] = user
    end

    context 'when valid parameters' do
      before do
        stub_request(:get, "https://graph.facebook.com/v19.0/#{whatsapp_template.whatsapp_template_id}")
        .with(
          headers: {
            Authorization: "Bearer #{ConnectedAccount.decrypt(connected_account.access_token)}"
          }
        )
        .to_return(status: 200, body: { status: APPROVED }.to_json)
      end

      it 'updates status' do
        described_class.new(params).sync_status
        expect(whatsapp_template.reload.status).to eq('APPROVED')
      end
    end

    context 'when invalid parameters' do
      context 'when user does not have permission to update' do
        before { expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'update').and_return(false) }

        it 'raises error' do
          expect(Rails.logger).to receive(:error).with("User doesn't have permission to update whatsapp templates")
          expect { described_class.new(params).sync_status }.to raise_error(ExceptionHandler::AuthenticationError, "022002||Unauthorized access.")
        end
      end

      context 'when user has read all but not update all' do
        before do
          whatsapp_template.update(created_by: create(:user, tenant_id: user.tenant_id))
          expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'update').and_return(true)
          expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'read').and_return(true)
          expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'read_all').and_return(true)
          expect_any_instance_of(Auth::Data).to receive(:can_access?).with('whatsappTemplate', 'update_all').and_return(false)
        end

        it 'raises error' do
          expect(Rails.logger).to receive(:error).with("User doesn't have permission to update whatsapp templates")
          expect { described_class.new(params).sync_status }.to raise_error(ExceptionHandler::AuthenticationError, "022002||Unauthorized access.")
        end
      end
    end
  end

  describe '#get_value' do
    let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id, created_by: user) }
    let(:whatsapp_template) { create(:whatsapp_template, connected_account: connected_account, created_by: user, tenant_id: user.tenant_id) }
    let(:service) { described_class.new({}) }

    before do
      Thread.current[:auth] = auth_data
      Thread.current[:user] = user
    end

    context 'when field type is MONEY' do
      let(:variable_mapping) { create(:variable_mapping, parent_entity: 'lead', internal_name: 'amount', field_type: 'MONEY', whatsapp_template: whatsapp_template) }
      let(:currency_response) do
        {
          'CURRENCY' => [
            { 'id' => 'USD', 'name' => 'US Dollar' },
            { 'id' => 'EUR', 'name' => 'Euro' },
            { 'id' => 'INR', 'name' => 'Indian Rupee' }
          ]
        }
      end

      before do
        allow(service).to receive(:get_standard_picklist).and_return(currency_response)
      end

      context 'when raw_value is present and is a hash with currencyId' do
        let(:entity_data) { { 'amount' => { 'value' => '1000.50', 'currencyId' => 'USD' } } }

        it 'returns formatted money value with currency' do
          result = service.send(:get_value, entity_data, variable_mapping)
          expect(result).to eq('1000.50 US Dollar')
        end

        it 'calls get_currency_from_currency_id with correct currency_id' do
          expect(service).to receive(:get_currency_from_currency_id).with('USD').and_return('US Dollar')
          service.send(:get_value, entity_data, variable_mapping)
        end
      end

      context 'when raw_value is not present' do
        let(:entity_data) { { 'other_field' => 'value' } }

        it 'returns empty string' do
          result = service.send(:get_value, entity_data, variable_mapping)
          expect(result).to eq('')
        end
      end

      context 'when raw_value is nil' do
        let(:entity_data) { { 'amount' => nil } }

        it 'returns empty string' do
          result = service.send(:get_value, entity_data, variable_mapping)
          expect(result).to eq('')
        end
      end
    end

    context 'when field type is PIPELINE_STAGE' do
      let(:variable_mapping) { create(:variable_mapping, parent_entity: 'lead', internal_name: 'stage', field_type: 'PIPELINE_STAGE', whatsapp_template: whatsapp_template) }

      context 'when raw_value is present and is a hash with name' do
        let(:entity_data) { { 'stage' => { 'name' => 'Qualified' } } }

        it 'returns pipeline stage name' do
          result = service.send(:get_value, entity_data, variable_mapping)
          expect(result).to eq('Qualified')
        end
      end

      context 'when raw_value is not present' do
        let(:entity_data) { { 'other_field' => 'value' } }

        it 'returns empty string' do
          result = service.send(:get_value, entity_data, variable_mapping)
          expect(result).to eq('')
        end
      end

      context 'when raw_value is nil' do
        let(:entity_data) { { 'stage' => nil } }

        it 'returns empty string' do
          result = service.send(:get_value, entity_data, variable_mapping)
          expect(result).to eq('')
        end
      end
    end
  end

  describe '#sync_templates' do
    let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id, created_by: user) }
    let(:params) { { connected_account_id: connected_account.id, entity_type: 'lead' } }

    before do
      Thread.current[:auth] = auth_data
      Thread.current[:user] = user
    end

    context 'when sync_whatsapp_template_active_job_count is not 0' do
      before do
        connected_account.update(sync_whatsapp_template_active_job_count: 5)
      end

      it 'raises Error if job is already in progress with the correct message' do
        expect(Rails.logger).to receive(:error).with("Sync whatsapp templates job already running for this connected account, connected account id: #{connected_account.id}, Tenant id: #{auth_data.tenant_id}")

        expect { described_class.new(params).sync_templates }.to raise_error(ExceptionHandler::WhatsappTemplatesSyncInProgress, "#{ErrorCode.whatsapp_template_sync_in_progress}||#{I18n.t('error.already_running_sync_job')}")
      end
    end

    context 'when sync_whatsapp_template_active_job_count is 0' do
      before do
        connected_account.update(sync_whatsapp_template_active_job_count: 0)
      end

      it 'calls FetchWhatsappTemplatesFromMetaService' do
        expect_any_instance_of(FetchWhatsappTemplatesFromMetaService).to receive(:fetch)

        described_class.new(params).sync_templates
      end
    end
  end

  describe '#delete_all_templates' do
    let(:connected_account) { create(:connected_account, tenant_id: user.tenant_id, created_by: user) }
    let(:another_connected_account) { create(:connected_account, tenant_id: user.tenant_id, created_by: another_user) }
    let(:params) { { connected_account_id: connected_account.id } }

    before do
      Thread.current[:auth] = auth_data
      Thread.current[:user] = user
    end

    context 'when connected account exists and has templates' do
      let!(:template1) { create(:whatsapp_template, connected_account: connected_account, name: 'Template 1') }
      let!(:template2) { create(:whatsapp_template, connected_account: connected_account, name: 'Template 2') }
      let!(:template3) { create(:whatsapp_template, connected_account: another_connected_account, name: 'Template 3') }

      before do
        # Create variable mappings for templates
        create(:variable_mapping, whatsapp_template: template1, component_type: HEADER, template_variable: 1)
        create(:variable_mapping, whatsapp_template: template1, component_type: BODY, template_variable: 1)
        create(:variable_mapping, whatsapp_template: template2, component_type: BODY, template_variable: 1)
        create(:variable_mapping, whatsapp_template: template3, component_type: HEADER, template_variable: 1)
      end

      it 'deletes all templates and their associations for the connected account' do
        expect {
          described_class.new(params).delete_all_templates
        }.to change(WhatsappTemplate, :count).by(-2)
         .and change(WhatsappTemplateComponent, :count).by(-14) # Each template has 7 components by default
         .and change(VariableMapping, :count).by(-3) # Only template1 and template2 variable mappings
      end

      it 'does not delete templates from other connected accounts' do
        described_class.new(params).delete_all_templates

        expect(WhatsappTemplate.exists?(template3.id)).to be_truthy
        expect(template3.components.count).to eq(7)
        expect(template3.variable_mappings.count).to eq(1)
      end

      it 'logs deletion of each template' do
        expect(Rails.logger).to receive(:info).with("WhatsappTemplateService | Template deleted for template id: #{template1.id}, connected account id: #{connected_account.id}, Tenant id: #{connected_account.tenant_id}").once
        expect(Rails.logger).to receive(:info).with("WhatsappTemplateService | Template deleted for template id: #{template2.id}, connected account id: #{connected_account.id}, Tenant id: #{connected_account.tenant_id}").once

        described_class.new(params).delete_all_templates
      end

      it 'destroys components and variable mappings before destroying templates' do
        # Verify that the templates are actually destroyed along with their associations
        expect {
          described_class.new(params).delete_all_templates
        }.to change { template1.reload rescue nil }.to(nil)
         .and change { template2.reload rescue nil }.to(nil)
         .and change { template1.components.count rescue 0 }.to(0)
         .and change { template2.components.count rescue 0 }.to(0)
         .and change { template1.variable_mappings.count rescue 0 }.to(0)
         .and change { template2.variable_mappings.count rescue 0 }.to(0)
      end
    end

    context 'when connected account has no templates' do
      it 'completes successfully without errors' do
        expect {
          described_class.new(params).delete_all_templates
        }.not_to change(WhatsappTemplate, :count)
      end

      it 'does not log any template deletions' do
        expect(Rails.logger).not_to receive(:info)

        described_class.new(params).delete_all_templates
      end
    end

    context 'when connected account does not exist' do
      let(:params) { { connected_account_id: -1 } }

      it 'raises NotFound error' do
        expect {
          described_class.new(params).delete_all_templates
        }.to raise_error(ExceptionHandler::NotFound, "022006||Connected Account not found")
      end
    end

    context 'when connected account belongs to different tenant' do
      let(:other_tenant_account) { create(:connected_account, tenant_id: user.tenant_id + 1) }
      let(:params) { { connected_account_id: other_tenant_account.id } }

      it 'raises NotFound error' do
        expect {
          described_class.new(params).delete_all_templates
        }.to raise_error(ExceptionHandler::NotFound, "022006||Connected Account not found")
      end
    end
  end

  describe '#retry_message_delivery' do
    let(:connected_account) { create(:connected_account, created_by: user, status: ACTIVE) }
    let!(:whatsapp_template) { create(:whatsapp_template, connected_account: connected_account, updated_by: another_user, status: 'APPROVED', entity_type: 'lead') }
    let(:user_share_rule) { create(:share_rule, share_rule_id: 3654, name: 'Share rule name updated', tenant_id: user.tenant_id, from_id: 4010, to_id: user.id, from_type: 'USER', to_type: 'USER', entity_id: 34343, entity_type: 'LEAD') }
    let(:conversation) { create(:conversation, connected_account_id: connected_account.id, tenant_id: user.tenant_id, phone_number: '+************', owner_id: user.id) }
    let!(:sub_conversation) { create(:sub_conversation, connected_account_id: connected_account.id, tenant_id: user.tenant_id, conversation_id: conversation.id) }
    let!(:message) { create(:message, whatsapp_template_id: whatsapp_template.id, connected_account: connected_account, owner_id: user.id, tenant_id: user.tenant_id, conversation_id: conversation.id, status: 'sending') }
    let(:params) do
      {
        message_id: message.id,
        whatsapp_template_id: whatsapp_template.id,
        entity_type: 'lead',
        entity_id: 34343,
        phone_number: entity_data['phoneNumbers'].first,
        entity_data: entity_data,
        bulk_job: true,
        retry_config: {
          noOfTimes: 3,
          timesRetried: 2
        }
      }
    end

    def entity_data
      lead_response = JSON.parse(file_fixture('get_lead_response.json').read)
      entity_details = lead_response['content'][0]
      entity_details['metaData'] = lead_response['metaData']
      entity_details.with_indifferent_access
      entity_details['ownerId'] = user.id
      entity_details
    end

    before do
      Thread.current[:auth] = auth_data
      Thread.current[:token] = valid_auth_token.token
      Thread.current[:user] = user

      whatsapp_template.components.where(component_type: BODY).update(component_text: 'lead first name')
      whatsapp_template.components.where.not(component_type: BODY).destroy_all

        allow(DateTime).to receive(:now).and_return('12:01 AM'.to_time)
        permissions = Thread.current[:auth].permissions.as_json.map do |permission|
          permission['action'].transform_keys! { |key| key.to_s.camelize(:lower) }
          permission
        end

        token_without_pid = GenerateToken.new(Thread.current[:user].id, Thread.current[:auth].tenant_id, permissions, true).call

        stub_request(:get, "http://localhost:8081/v1/tenants").with(
          headers: {
          'Accept'=>'*/*',
          'Authorization'=>"Bearer #{valid_auth_token.token}",
          }
        ).to_return(status: 200, body: file_fixture('get_tenant_response.json').read, headers: {})

        user_resp = JSON.parse(file_fixture('get_users_response.json').read)
        user_resp['content'][2]['id'] = user.id
        stub_request(:post, "http://localhost:8081/v1/users/search").with(
          body: {
            fields: %w[timezone dateFormat firstName lastName],
            jsonRule: {
              rules: [
                {
                  operator: 'in',
                  id: 'id',
                  field: 'id',
                  type: 'double',
                  value: "4010,#{user.id}"
                }
              ],
              condition: 'AND',
              valid: true
            }
          }.to_json,
          headers: {
            'Accept'=>'application/json',
            'Authorization'=>"Bearer #{valid_auth_token.token}",
            'Content-Type'=>'application/json',
          }
        ).to_return(status: 200, body: user_resp.to_json, headers: {})

        stub_request(:get, "http://localhost:8086/v1/picklists/standard").with(
              headers: {
              'Accept'=>'*/*',
              'Authorization'=>"Bearer #{valid_auth_token.token}",
              }
            ).to_return(status: 200, body: file_fixture('standard_picklists.json').read, headers: {})

        stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages").with(
          body: {
            messaging_product: 'whatsapp',
            recipient_type: 'individual',
            to: '+************',
            type: 'template',
            template: {
              name: whatsapp_template.whatsapp_template_namespace,
              language: {
                code: 'en'
              },
              components: [
                {
                  type: 'header',
                  parameters: []
                },
                {
                  type: 'body',
                  parameters: []
                }
              ]
            }
          }.to_json,
          headers: {
            'x-access-token': 'partner-token',
            'x-waba-id': connected_account.waba_id,
            'Content-Type'=>'application/json',
          }).to_return(status: 200, body: file_fixture('/facebook/whatsapp_template/send-message-success-response.json'), headers: {})
    end

    context 'when message is not found' do
      before do
        whatsapp_credit
        message.destroy!
      end

      it 'raises error' do
        expect { described_class.new(params).retry_message_delivery }.to raise_error(ExceptionHandler::NotFound, '022033||Retryable Message not found')
      end
    end

    context 'when message is found' do
      context 'when credits are available' do
        before { whatsapp_credit }

        context 'when message is sent successfully' do
          before { message.update!(metadata: { 'executedWorkflows' => [], 'retryConfig' => { 'noOfTimes' => 3, 'timesRetried' => 1 } }) }

          it 'returns message id' do
            result = nil

            expect {
              result = described_class.new(params).retry_message_delivery
            }.to change(Message, :count).by(0)
            
            expect(result).to eq(message.id)
            expect(message.reload.metadata).to eq({
              'executedWorkflows' => [],
              'retryConfig' => {
                'noOfTimes' => 3,
                'timesRetried' => 2
              }
            })
          end
        end

        context 'when message fails to send' do
          before do
            stub_request(:post, "https://amped-express.interakt.ai/api/v17.0/#{connected_account.phone_number_id}/messages").to_return(status: 400, body: file_fixture('facebook/message/session-message-failure-response.json'))
            expect(Publishers::WhatsappMessageStatusUpdatedPublisher).to receive(:call).once
            expect(Publishers::WhatsappMessageStatusUpdatedV2Publisher).to receive(:call).once
          end

          it 'updates message status to failed and raises error' do
            expect {
              described_class.new(params).retry_message_delivery
            }.to raise_error(ExceptionHandler::ThirdPartyAPIError, '022017||(#131006) Resource not found. unknown contact')
              expect(message.reload.status).to eq('failed')
              expect(message.failed_at).to be_present
          end
        end
      end

      context 'when credits are not available' do
        it 'does not update message status and raises error' do
          expect { described_class.new(params).retry_message_delivery }.to raise_error(ExceptionHandler::InsufficientWhatsappCreditsBalance, '022028||Insufficient whatsapp credits balance for bulk or workflow action')
          expect(message.reload.status).to eq('sending')
        end
      end
    end
  end
end
