# frozen_string_literal: true

class ConnectedAccountService
  extend MessageSerializer::Helper

  def initialize(params)
    @params = params
    @auth_data = Thread.current[:auth]
    @current_user = Thread.current[:user]
  end

  def get_all
    unless @auth_data.can_access?('whatsappBusiness', 'read_all')
      Rails.logger.error "User doesn't have permission to view connected account"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end

    ConnectedAccount.where(tenant_id: @auth_data.tenant_id).order(created_at: :desc)
  end

  def show
    unless @auth_data.can_access?('whatsappBusiness', 'read_all')
      Rails.logger.error "User doesn't have permission to view connected account"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end

    connected_account = ConnectedAccount.find_by(tenant_id: @auth_data.tenant_id, id: @params[:id])
    unless connected_account.present?
      raise(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found', type: 'Account')}")
    end

    connected_account
  end

  def create
    unless @auth_data.can_access?('whatsappBusiness', 'update_all')
      Rails.logger.info "User doesn't have permission to create connected account"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end

    connected_account = ConnectedAccount.where(phone_number_id: @params[:phone_number_id], tenant_id: @auth_data.tenant_id).first
    connected_account ||= ConnectedAccount.new(
      @params.slice(:phone_number_id, :waba_id, :entities_to_create)
      .merge(
        tenant_id: @auth_data.tenant_id,
        status: DRAFT,
        interakt_onboarding_status: DRAFT_STATUS,
        created_by: @current_user,
        updated_by: @current_user
      )
    )

    credits_revised_at = Date.today.in_time_zone('Asia/Calcutta').beginning_of_day.to_i

    if connected_account.valid?
      access_token_response = Facebook::AuthCode.new(@params[:auth_code]).exchange
      connected_account.access_token = ConnectedAccount.encrypt(access_token_response.body['access_token'])
      phone_number_info = Facebook::PhoneNumber.new(connected_account).find.body
      connected_account.assign_attributes({
        name: phone_number_info['verified_name'],
        waba_number: phone_number_info['display_phone_number'].delete(' '),
        phone_number_id: phone_number_info['id'],
        waba_id: @params[:waba_id],
        entities_to_create: @params[:entities_to_create],
        status: DRAFT,
        interakt_onboarding_status: DRAFT_STATUS,
        updated_by: @current_user,
        credits_revised_upto: credits_revised_at
      })
    end

    if connected_account.valid? && connected_account.save
      whatsapp_credit = WhatsappCredit.find_by(tenant_id: @auth_data.tenant_id)
      WhatsappCredit.create(
        total: 0,
        consumed: 0,
        parked: 0,
        tenant_id: @auth_data.tenant_id,
        credits_revised_at: credits_revised_at
      ) unless whatsapp_credit

      connected_account
    else
      raise(
        ExceptionHandler::InvalidDataError,
        "#{ErrorCode.invalid_data}||#{I18n.t('error.invalid_data', error: connected_account.errors.full_messages.join(', '))}"
      )
    end
  end

  def update
    connected_account = ConnectedAccount.find_by(id: @params[:id], tenant_id: @auth_data.tenant_id)
    raise(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found', type: 'Connected Account')}") if connected_account.blank?

    unless @auth_data.can_access?('whatsappBusiness', 'update_all')
      Rails.logger.info "User doesn't have permission to update connected account"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end

    if(connected_account.interakt_onboarding_status == DRAFT_STATUS)
      Interakt::Onboarding.new(connected_account).signup
      connected_account.update(updated_by: @current_user, interakt_onboarding_status: REQUEST_SENT, status: PENDING)
    end

    connected_account.assign_attributes(@params.slice(:display_name, :entities_to_create).merge(updated_by: @current_user))
    if connected_account.save
      connected_account
    else
      raise(
        ExceptionHandler::InvalidDataError,
        "#{ErrorCode.invalid_data}||#{I18n.t('error.invalid_data', error: connected_account.errors.full_messages.join(', '))}"
      )
    end
  end

  def activate
    connected_account = ConnectedAccount.find_by(id: @params[:id], tenant_id: @auth_data.tenant_id)
    raise(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found', type: 'Connected Account')}") if connected_account.blank?

    unless @auth_data.can_access?('whatsappBusiness', 'update_all')
      Rails.logger.error "User doesn't have permission to update connected account"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end

    if connected_account.interakt_onboarding_status == WABA_ONBOARDED
      connected_account.update(status: ACTIVE, deactivated_at: nil)
    else
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid}||#{I18n.t('error.connected_account.cannot_activate_account_contact_support')}")
    end

    connected_account
  end

  def deactivate
    connected_account = ConnectedAccount.find_by(id: @params[:id])
    raise(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found', type: 'Connected Account')}") if connected_account.blank?

    connected_account.update(status: INACTIVE, deactivated_at: DateTime.now.to_i)

    connected_account
  end

  def send_verification_code
    connected_account = ConnectedAccount.find_by(id: @params[:id], tenant_id: @auth_data.tenant_id)
    raise(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found', type: 'Connected Account')}") if connected_account.blank?

    unless @auth_data.can_access?('whatsappBusiness', 'update_all')
      Rails.logger.error "User doesn't have permission to send verification code for connected account"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end

    unless %w[SMS VOICE].include?(@params[:code_method])
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_data}||#{I18n.t('error.invalid_data', error: 'Invalid code method')}")
    end

    Facebook::PhoneNumber.new(connected_account).request_code(@params[:code_method], @params[:language].presence || 'en')

    connected_account
  end

  def verify_code
    connected_account = ConnectedAccount.find_by(id: @params[:id], tenant_id: @auth_data.tenant_id)
    raise(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found', type: 'Connected Account')}") if connected_account.blank?

    unless @auth_data.can_access?('whatsappBusiness', 'update_all')
      Rails.logger.error "User doesn't have permission to verify code for connected account"
      raise(ExceptionHandler::AuthenticationError, "#{ErrorCode.unauthorized}||#{I18n.t('error.unauthorized')}")
    end

    Facebook::PhoneNumber.new(connected_account).verify_code(@params[:otp_code])
    connected_account.update_column(:is_verified, true)

    connected_account
  end

  def deactivate_all_accounts
    ConnectedAccount.where(tenant_id: @params['tenantId']).where.not(status: INACTIVE).update_all(status: INACTIVE, deactivated_at: DateTime.now.to_i)
  end

  def lookup
    # TODO Add waba onboarded connected accounts only
    connected_accounts =
      ConnectedAccount.where(tenant_id: @auth_data.tenant_id)

    if @params[:view] == 'billing'
      connected_accounts = connected_accounts.where(status:[ACTIVE, INACTIVE])
    else
      connected_accounts = connected_accounts.where(status: ACTIVE, is_verified: true)
    end

    if @params[:entity_type].present?
      connected_accounts = connected_accounts.where(id: AgentUser.select(:connected_account_id).where(tenant_id: @auth_data.tenant_id, user_id: @current_user.id, entity_type: @params[:entity_type]))
    else
      connected_accounts = connected_accounts.where(id: AgentUser.select(:connected_account_id).where(tenant_id: @auth_data.tenant_id, user_id: @current_user.id))
    end

    connected_accounts.order(last_message_received_at: :desc)

    if @params[:q].present?
      connected_accounts = connected_accounts.where(ConnectedAccount.arel_table[:display_name].matches("%#{ConnectedAccount.sanitize_sql_like(@params[:q])}%"))
    end

    connected_accounts
  end

  def entity_phones
    if [LOOKUP_CONTACT, LOOKUP_LEAD].exclude?(@params[:entity_type])
      raise(ExceptionHandler::InvalidDataError, "#{ErrorCode.invalid_data}||#{I18n.t('error.invalid_data', error: 'Unsupported entity')}")
    end

    # TODO Add waba onboarded connected accounts only
    connected_account_exists = ConnectedAccount.where(
      id: @params[:id],
      tenant_id: @auth_data.tenant_id,
      status: ACTIVE,
      is_verified: true
    ).exists?

    raise(ExceptionHandler::NotFound, "#{ErrorCode.not_found}||#{I18n.t('error.not_found', type: 'Connected Account')}") unless connected_account_exists

    entity_data = EntityService.new({ entity_id: @params[:entity_id], entity_type: @params[:entity_type] }).get_by_id

    phone_numbers = entity_data['phoneNumbers'].present? ? entity_data['phoneNumbers'] : []

    LookUp.where(entity_id: @params[:entity_id], entity_type: @params[:entity_type], tenant_id: @auth_data.tenant_id).each do |look_up|
      look_up_phone_number_with_plus = look_up.phone_number&.starts_with?('+') ? look_up.phone_number : "+#{look_up.phone_number}"

      if look_up.conversations.where(connected_account_id: @params[:id]).count > 0 && !phone_numbers.any? { |num| "#{num['dialCode']}#{num['value']}" === look_up_phone_number_with_plus }
        parsed_phone = Phonelib.parse(look_up.phone_number)

        phone_numbers << {
          'dialCode' => "+#{parsed_phone.country_code}",
          'value' => parsed_phone.raw_national,
          'disabled' => true
        }
      end
    end

    conversation_owners = []
    twenty_four_hours_ago = 24.hours.ago

    phone_numbers.each do |phone|
      conversation_params = {
        tenant_id: @auth_data.tenant_id,
        connected_account_id: @params[:id],
        phone_number: "#{phone['dialCode']}#{phone['value']}"
      }

      phone_number_conversation = Conversation.find_by(conversation_params)

      unless phone_number_conversation.present?
        unless @current_user.can_send_conversation_message?(@params[:entity_id], @params[:entity_type], entity_data['ownerId'])
          Rails.logger.info "User does not have conversation create permission, User id: #{@current_user.id}, Tenant id: #{@auth_data.tenant_id}"
          raise(ExceptionHandler::MessageNotAllowedError, ErrorCode.message_not_allowed)
        end

        phone_number_conversation = Conversation.new(conversation_params)
        phone_number_conversation.owner_id = @current_user.id
        phone_number_conversation.status = NEW
        phone_number_conversation.save!
        SubConversation.create!(
          tenant_id: @auth_data.tenant_id,
          connected_account_id: @params[:id],
          conversation_id: phone_number_conversation.id,
          status: NEW
        )

        look_up = GetLookUpWithOwner.call({ id: @params[:entity_id], entity: @params[:entity_type], phone_number: "#{phone['dialCode']}#{phone['value']}", name: "#{entity_data['firstName']} #{entity_data['lastName']}".strip, owner_id: entity_data['ownerId'] })
        look_up.conversations << phone_number_conversation

        AssociateConversationWithEntitiesJob.perform_later(phone_number_conversation.id, LOOKUP_LEAD, { dialCode: phone['dialCode'], value: phone['value'] })
        AssociateConversationWithEntitiesJob.perform_later(phone_number_conversation.id, LOOKUP_CONTACT, { dialCode: phone['dialCode'], value: phone['value'] })
      end

      conversation_owners << phone_number_conversation&.owner_id
      phone[:conversationId] = phone_number_conversation&.id
      last_message_received_at = phone_number_conversation.last_message_received_at
      
      if last_message_received_at.present?
        phone[:session] = last_message_received_at < twenty_four_hours_ago || phone['disabled'] ? INACTIVE : ACTIVE
        phone[:lastContactedAt] = last_message_received_at
      else
        phone[:session] = INACTIVE
        phone[:lastContactedAt] = nil
      end
    end

    unless @current_user.can_user_read_conversation?(@params[:entity_id], @params[:entity_type], entity_data['ownerId'], conversation_owners)
      Rails.logger.info "User does not have conversation permission on #{@params[:entity_type]}, User id: #{@current_user.id}, Tenant id: #{@auth_data.tenant_id}"
      raise(ExceptionHandler::MessageNotAllowedError, ErrorCode.message_not_allowed)
    end

    return phone_numbers unless phone_numbers.present?

    masking_enabled = false
    entity_type = @params[:entity_type]
    if [LOOKUP_LEAD, LOOKUP_CONTACT, LOOKUP_DEAL].include?(entity_type)
      entity_type = LOOKUP_CONTACT if entity_type == LOOKUP_DEAL

      masked_fields = GetMaskedFields.new(entity_type).call
      masking_enabled = masked_fields.find { |field| field['name'] == 'phoneNumbers' }.present?
    end

    phone_numbers.map { |phone| phone['value'] = ConnectedAccountService.mask_number(phone['value'], with_country_code: false) } if masking_enabled

    phone_numbers = phone_numbers.sort_by { |phone_number| phone_number[:lastContactedAt].present? ? phone_number[:lastContactedAt].to_i : 0 }.reverse

    phone_numbers
  end
end
