Rails.application.routes.draw do
  mount Rswag::Api::Engine => '/v2'

  # For details on the DSL available within this file, see https://guides.rubyonrails.org/routing.html
  get 'health', to: 'application#health'
  get '/v06700edc6a3b7f12/messages/health', to: 'health#status'

  namespace :v1 do
    resources :messages, only: [:create, :destroy, :show, :update] do
      resources :attachments, only: [:show]
      member do
        post :mark_as_read, path: 'mark-as-read'
      end
      collection do
        post :search
        post :sync
        get 'whatsapp/d549620a777933f8b4e6401a8d41e3b4d93672b45276491029d7b31f343d9c82/webhooks', to: 'interakt_webhooks#challenge'
        post 'whatsapp/d549620a777933f8b4e6401a8d41e3b4d93672b45276491029d7b31f343d9c82/webhooks', to: 'interakt_webhooks#handler'
        get 'whatsapp/fc1656bb881e0e186c42c9d104836219524f2f073ee9ae49809bcdfc0cf69b63/interakt/webhooks', to: 'interakt_webhooks#challenge'
        post 'whatsapp/fc1656bb881e0e186c42c9d104836219524f2f073ee9ae49809bcdfc0cf69b63/interakt/webhooks', to: 'interakt_webhooks#handler'
        resources :connected_accounts, path: 'connected-accounts', only: [:index, :show, :create, :update] do
          collection do
            get :lookup
          end
          resources :template_media, only: :show, path: 'template-media' do
            collection do
              post '/', to: 'template_media#upload'
            end
          end

          post '/chatbot-media', to: 'chatbot_media#upload'
          get '/chatbot-media', to: 'chatbot_media#list'

          member do
            post :deactivate
            post :activate
            post 'request-code', to: 'connected_accounts#request_code'
            post 'verify-code', to: 'connected_accounts#verify_code'
            get '/:entity_type/:entity_id/phone-numbers', to: 'connected_accounts#entity_phones'
            post 'session-message', to: 'messages#session_message'
            post '/media-session-message', to: 'messages#media_session_message'
            delete 'delete-all-templates', to: 'whatsapp_templates#delete_all_templates'
          end

          resources :field_mappings, only: [:index, :create], path: 'mapped-fields/:entity_type'
          resources :agents, only: [] do
            collection do
              get ':entity_type', to: 'agents#index'
              post ':entity_type/save', to: 'agents#save'
            end
          end
        end

        resources :whatsapp_templates, only: [:create, :update, :show], path: 'whatsapp-templates' do
          collection do
            post 'create-and-submit', to: 'whatsapp_templates#create_and_submit'
            post :search
            get '/:entity/variables', to: 'whatsapp_templates#variables'
            get 'lookup'
            post 'sync', to: 'whatsapp_templates#sync'
          end

          member do
            post 'send', to: 'whatsapp_templates#send_message'
            post 'send-bulk-message', to: 'whatsapp_templates#send_bulk_message'
            get 'preview/:entity_type/:entity_id', to: 'whatsapp_templates#preview'
            put 'sync-status', to: 'whatsapp_templates#sync_status'

            post :deactivate
            resources :variable_mappings, only: [:index], path: 'variable-mappings' do
              collection do
                post :save
              end
            end
          end
        end

        resources :whatsapp_credits, only: [], path: 'whatsapp-credits' do
          collection do
            get :summary
            post :history
            get 'status', to: 'whatsapp_credits#status'
          end
        end

        resources :conversations, only: [:create], path: 'conversations' do
          collection do
            post :search, to: 'conversations#index'
            post 'by-entity', to: 'conversations#get_by_entity'
          end

          member do
            post :search
            delete :destroy
            get :permissions
            put 'complete', to: 'conversations#mark_as_completed'
          end
        end

        scope :whatsapp do
          resources :layout, only: [] do
            collection do
              get :list
            end
          end

          put ':message_id/retry', to: 'whatsapp_templates#retry_message_delivery'
        end
      end
    end

    get '/conversations/:conversation_id/messages/:message_id', to: "messages#show_conversation_message"
    get '/messages/whatsapp/:id', to: "messages#show_whatsapp_message"
  end
end
